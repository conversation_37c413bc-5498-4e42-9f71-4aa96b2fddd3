import json
import uuid
import  jsons
from flask import Flask, request, jsonify, abort
from  flask import render_template
# from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os
from werkzeug.utils import secure_filename
import base64
from application.config.readConf import readConf
from flask_sock import Sock
from application.Helpers.log_conf import Logger
from application.database import  central_db
from application.job.SendOrderJob import  Send<PERSON>rderJob
from dotenv import load_dotenv
from flask_cors import CORS
import logging
from application.Models.company import CompanyDevice, Company
from application.utils.db_connection import DatabaseConnection
from application.Helpers.device_logger import device_logger, log_device_info, log_device_error, log_device_warning, log_websocket_activity

# Load environment variables from .env file
load_dotenv()

os.environ["FLASK_ENV"] = "development"
os.environ["FLASK_DEBUG"] = "1"
app = Flask(__name__)
CORS(app, resources={
    r"/*": {
        "origins": ["http://localhost:3000","https://kazi-sync.onrender.com","https://sms.remmittance.com", "https://kazisync-front-end.vercel.app", "https://kazisync.com", "https://www.kazisync.com"],
        "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"],
        "supports_credentials": True
    }
})

# Define allowed hosts
ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    'kazi-sync.onrender.com',
    'sms.remmittance.com',
    'kazisync-front-end.vercel.app',
    'kazisync.com',
    'www.kazisync.com'
]

@app.before_request
def validate_host():
    """Validate the host header to prevent host header attacks."""
    host = request.host.split(':')[0]  # Remove port if present
    app.logger.info(f"Request: {request.method} {request.path}")
    app.logger.info(f"Checking host: '{host}' against allowed hosts: {ALLOWED_HOSTS}")
    app.logger.info(f"Origin header: {request.headers.get('Origin')}")

    if host not in ALLOWED_HOSTS:
        app.logger.warning(f"Invalid host header: {host}")
        abort(400, description="Invalid host header")
    else:
        app.logger.info(f"Host '{host}' is valid")

sock = Sock(app)
readConf_=readConf()

db_connection = DatabaseConnection()

#url=readConf_.GetDBParam()
url = os.getenv("SQLALCHEMY_DATABASE_URI")
secret_key = os.getenv('SECRET_KEY')

# Provide a dummy database to satisfy Flask-SQLAlchemy initialization
app.config["SQLALCHEMY_DATABASE_URI"] = os.getenv("SQLALCHEMY_DATABASE_URI")
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = secret_key

# Initialize SQLAlchemy
try:
    app.logger.info(f"Database URL: {url}")
    central_db.init_app(app)
    app.logger.info("Central database initialized")
except Exception as e:
    app.logger.error(f"Error initializing central database: {str(e)}")

# register the db_connection with the app
app.db_connection = db_connection


# Configure the Flask logger
handler = logging.FileHandler('app.log')
handler.setLevel(logging.INFO)  # Set the handler logging level

# Update formatter to include pathname, filename, and line number
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s - [in %(pathname)s:%(lineno)d]')
handler.setFormatter(formatter)

app.logger.addHandler(handler)
app.logger.setLevel(logging.INFO)

#region -----------长时任务开始---------------------------------------------
import atexit
send_order_job = SendOrderJob()
@app.before_request
def start_thread_once():
    if not send_order_job.is_running():
        print("start----------")
        send_order_job.start_thread()

atexit.register(send_order_job.stop_thread)
#endregion -----------长时任务结束---------------------------------------------
#region-----------web 处理开始---------------------------------------------
@app.route('/')
def index():  # put application's code here
    print(os.environ["FLASK_ENV"])
    APP_PATH =  request.base_url[:-1]
    print(APP_PATH)
    return render_template("index.html", APP_PATH=APP_PATH)


@app.route('/a')
def index2():
    data = {'key1': 'value1', 'key2': 'value2'}

    # 使用jsonify函数将data转换为JSON格式并返回
    return jsonify(data)

@app.route('/test-cors', methods=['GET', 'POST', 'OPTIONS'])
def test_cors():
    """Test endpoint to verify CORS is working"""
    app.logger.info(f"CORS test endpoint hit with method: {request.method}")
    app.logger.info(f"Origin: {request.headers.get('Origin')}")
    app.logger.info(f"Headers: {dict(request.headers)}")

    if request.method == 'OPTIONS':
        return jsonify({"message": "CORS preflight successful"}), 200
    else:
        return jsonify({"message": "CORS test successful", "method": request.method}), 200

from application.Models.Device import Device, insert_device, get_all_devices, get_device_by_id, get_device_by_serial_num, update_device
from application.Models.Person import Person, insert_person, select_person_by_id, delete_person_by_id, update_person_by_id,select_all
from application.Models.MachineCommand import MachineCommand, insert_machine_command, select_machine_command_by_id
from application.Models.EnrollInfo import EnrollInfo, insert_enroll_info, get_all_enroll_info,selectByBackupnum,update_enroll_info2
from application.Models.AccessDay import AccessDay,insert_access_day,get_all_access_days,get_access_day_by_id
from application.Models.AccessWeek import AccessWeek, insert_access_week, get_all_access_weeks, get_access_week_by_id
from application.Models.LockGroup import LockGroup
from application.Models.UserLock import UserLock
from application.Models.Records import Record, insert_record,select_all_records,insert_record2
from application.Models.Msg import Msg

from application.Services.PersonService import PersonService,PersonServiceImpl
from application.Services.EnrollInfoService import EnrollInfoService
from application.Services.AccessWeekService import AccessWeekService
from application.Services.LockService import LockGroupService
from application.Services.UserLockService import UserLockService


person_=Person()
enrollinfo=EnrollInfo()
enrollinfoserive=EnrollInfoService( enroll_info=enrollinfo,person=person_)
# machine_command_=MachineCommand()
# machinecommandservice=MachineCommandService(machine_command=MachineCommand)
personService = PersonServiceImpl(person=person_, enroll_info=enrollinfoserive, machine_command=MachineCommand())



@app.route('/device', methods=['POST'])
def create_device():

    data = request.get_json()  # Get data from JSON body
    serial_num = data.get('serial_num')
    status = data.get('status')
    insert_device(serial_num, status)
    return jsonify({"message": "Device created successfully."}), 200

@app.route('/device', methods=['GET'])
def get_all_device():
    print("get all device")
    update_status_by_primary_key(62, 1)
    device_list = get_all_devices()
    device_list = [device.to_dict() for device in device_list]  # Convert each Device to a dictionary
    return jsons.dump(Msg.success().add("device", device_list))

@app.route('/enrollInfo', methods=['GET'])
def get_all_enrollinfo():
    enroll_infoes = get_all_enroll_info() # Person.query.all()
    enroll_infoes = [enroll_info.to_dict() for enroll_info in enroll_infoes]
    return jsons.dump(Msg.success().add("enrollInfo", enroll_infoes))

@app.route('/sendWs', methods=['GET'])
def send_ws():
    device_sn = request.args.get('deviceSn')
    print(("device_sn:"+device_sn))
    message = "{\"cmd\":\"getuserlist\",\"stn\":true}"

    device_list = Device.query.all()
    for device in device_list:
        machine_command = MachineCommand(
            name="getuserlist",
            status=0,
            send_status=0,
            err_count=0,
            serial=device.serial_num,
            gmt_crate=datetime.now(),
            gmt_modified=datetime.now(),
            content=message
        )
        print(machine_command)
        db.session.add(machine_command)
    db.session.commit()

    return jsons.dump(Msg.success())


@app.route('/addPerson', methods=['POST'])
def add_person():
    person_temp = request.form
    pic = request.files['pic']
    # path = "C:/dynamicface/picture/"
    path=readConf().GetUploadParam()
    app.logger.info(f"Path: {path}")
    photo_name = ""
    new_name = ""
    if pic:
        if pic.filename:
            photo_name = secure_filename(pic.filename)
            new_name = str(uuid.uuid4()) + photo_name[photo_name.rfind('.'):]
            photo_file = os.path.join(path, new_name)
            pic.save(photo_file)

    person = {
        'id': person_temp.get('userId'),
        'name': person_temp.get('name'),
        'roll_id': person_temp.get('privilege')
    }
    existing_person = select_person_by_id(person_temp.get('userId'))
    app.logger.info(f"Existing person: {existing_person}")

    if not existing_person:
        try:
            result = insert_person(**person)
            app.logger.info(f"Person inserted: {result}")
        except Exception as e:
            app.logger.exception(f"Error inserting person: {str(e)}")
            return jsons.dump(Msg.fail())

    if person_temp.get('password'):
        app.logger.info(f"Password: {person_temp.get('password')}")
        enroll_info_temp2 = {
            'backupnum': 10,
            'enroll_id': person_temp.get('userId'),
            'signatures': person_temp.get('password')
        }
        app.logger.info(f"Enroll info: {enroll_info_temp2}")
        try:
            result2 = insert_enroll_info(**enroll_info_temp2)
            app.logger.info(f"Enroll info inserted: {result2}")
        except Exception as e:
            app.logger.exception(f"Error inserting enroll info: {str(e)}")
            return jsons.dump(Msg.fail())

    if person_temp.get('cardNum'):
        enroll_info_temp3 = {
            'backupnum': 11,
            'enroll_id': person_temp.get('userId'),
            'signatures': person_temp.get('cardNum')
        }
        insert_enroll_info(**enroll_info_temp3)

    if new_name:
        with open(os.path.join(path, new_name), "rb") as image_file:
            base64_str = base64.b64encode(image_file.read()).decode()
            app.logger.info(f"Base64 string: {base64_str}")

        enroll_info_temp = {
            'backupnum': 50,
            'enroll_id': person_temp.get('userId'),
            'imagepath': new_name,
            'signatures': base64_str
        }
        app.logger.info(f"Enroll info: {enroll_info_temp}")
        try:
            result3 = insert_enroll_info(**enroll_info_temp)
            app.logger.info(f"Enroll info inserted: {result3}")
        except Exception as e:
            app.logger.exception(f"Error inserting enroll info: {str(e)}")
            return jsons.dump(Msg.fail())
    else:
        enroll_info_temp = {
            'backupnum': 50,
            'enroll_id': person_temp.get('userId'),
            'imagepath': "",
            'signatures': ""
        }
        try:
            result4 = insert_enroll_info(**enroll_info_temp)
            app.logger.info(f"Enroll info inserted: {result4}")
            return jsons.dump(Msg.success())
        except Exception as e:
            app.logger.exception(f"Error inserting enroll info: {str(e)}")
            return jsons.dump(Msg.fail())

@app.route('/addEmployee', methods=['POST'])
def addemployee():
    person_temp = request.get_json()  # Get JSON data instead of form
    app.logger.info(f"data retrieved from json: {person_temp}")
    pic = request.files.get('pic')  # Get file if provided
    path = readConf().GetUploadParam()

    app.logger.info(f"Path: {path}")
    photo_name = ""
    new_name = ""

    if pic and pic.filename:
        photo_name = secure_filename(pic.filename)
        new_name = str(uuid.uuid4()) + photo_name[photo_name.rfind('.'):]
        photo_file = os.path.join(path, new_name)
        pic.save(photo_file)

    employee_id = person_temp.get('employee_id')


    person = {
        'name': person_temp.get('name'),
        'roll_id': person_temp.get('privilege'),
        'employee_id': employee_id
    }

    """try:
        existing_person = select_person_by_id(person_temp.get('userId'))
        app.logger.info(f"Existing person: {existing_person}")
    except Exception as e:
        app.logger.error(f"Error getting existing person: {str(e)}")
        return jsons.dump(Msg.fail())
    """

    company_id = person_temp.get('company_id')

    # Get the database for the company
    try:
        database_name = Company.get_database_given_company_id(company_id)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        return jsons.dump(Msg.fail())

    # Connecr to the database:
    with db_connection.get_session(database_name) as session:
        # Insert the person in the database
        try:
            result = Person.add_employee(session, **person)
            app.logger.info(f"Person inserted: {result}")
        except Exception as e:
            app.logger.exception(f"Error inserting person: {str(e)}")
            return jsons.dump(Msg.fail())
        # Get userId from the person_temp dictionary
        try:
            userId = result.to_dict().get('id')
            app.logger.info(f"User ID: {userId}")
        except Exception as e:
            app.logger.error(f"Error getting user ID: {str(e)}")
            return jsons.dump(Msg.fail())

        if person_temp.get('password'):

            enroll_info_temp2 = {
                'backupnum': 10,
                'enroll_id': userId,
                'signatures': person_temp.get('password')
            }
            try:
                result2 = insert_enroll_info(session, **enroll_info_temp2)
                app.logger.info(f"Enroll info inserted: {result2}")
            except Exception as e:
                app.logger.exception(f"Error inserting enroll info: {str(e)}")
                return jsons.dump(Msg.fail())

        if person_temp.get('cardNum'):
            enroll_info_temp3 = {
                'backupnum': 11,
                'enroll_id': userId,
                'signatures': person_temp.get('cardNum')
            }
            insert_enroll_info(session, **enroll_info_temp3)

        if new_name:
            with open(os.path.join(path, new_name), "rb") as image_file:
                base64_str = base64.b64encode(image_file.read()).decode()
                app.logger.info(f"Base64 string: {base64_str}")

            enroll_info_temp = {
                'backupnum': 50,
                'enroll_id': userId,
                'imagepath': new_name,
                'signatures': base64_str
            }
            try:
                result3 = insert_enroll_info(session, **enroll_info_temp)
                app.logger.info(f"Enroll info inserted: {result3}")
            except Exception as e:
                app.logger.exception(f"Error inserting enroll info: {str(e)}")
                return jsons.dump(Msg.fail())
        else:
            enroll_info_temp = {
                'backupnum': 50,
                'enroll_id': userId,
                'imagepath': "",
                'signatures': ""
            }
            try:
                result4 = insert_enroll_info(session, **enroll_info_temp)
                app.logger.info(f"Enroll info inserted: {result4}")
                return jsonify({
                    "message": "Employee added successfully",
                    "userId": userId,
                    "employee_id": employee_id,
                    "name": person_temp.get('name')
                })

            except Exception as e:
                app.logger.exception(f"Error inserting enroll info: {str(e)}")
                return jsons.dump(Msg.fail())

@app.route('/getUserInfo', methods=['GET'])
def get_user_info():
    print("Get user info")
    device_sn = request.args.get('deviceSn')
    persons = Person.query.all()
    enrolls_prepared = []
    for person in persons:
        enroll_infos = EnrollInfo.query.filter_by(enroll_id=person.id).all()
        for enroll_info in enroll_infos:
            if enroll_info.enroll_id and enroll_info.backupnum:
                enrolls_prepared.append(enroll_info)
    print("Collecting user data: ", enrolls_prepared)
    PersonServiceImpl.get_signature2(enrolls_prepared, device_sn)

    return jsons.dump(Msg.success())

@app.route('/get_users', methods=['GET'])
def get_users():
    """Get all users from the database."""
    # Get the company_id from the request
    company_id = request.args.get('company_id')
    app.logger.info(f"Company ID: {company_id}")

    # Get database name from the company_id
    try:
        database_name = Company.get_database_given_company_id(company_id)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        return jsons.dump(Msg.fail())

    # Connect to the database
    with db_connection.get_session(database_name) as session:
        persons = Person.get_persons(session)
        persons = [person.to_dict() for person in persons]
        app.logger.info(f"Persons: {persons}")
        return jsons.dump(Msg.success().add("persons", persons))

@app.route('/sendGetUserInfo', methods=['GET'])
def send_get_user_info():
    enroll_id = request.args.get('enrollId', type=int)
    backupnum = request.args.get('backupNum', type=int)
    device_sn = request.args.get('deviceSn')

    device_list = Device.query.all()
    print("Device info: ", device_list)

    message = {"cmd": "getuserinfo", "enrollid": enroll_id, "backupnum": backupnum}

    MachineCommand.add_get_one_user_command(enroll_id, backupnum, device_sn)

    return jsons.dump(Msg.success())


@app.route('/setPersonToDevice', methods=['GET']) #2024年1月8日16:53:53
def send_set_user_info():
    device_sn = request.args.get('deviceSn')

    personService.setUserToDevice2(device_sn)

    return jsons.dump(Msg.success())


@app.route('/setUsernameToDevice', methods=['GET']) #2024年1月8日18:06:22
def set_username_to_device():
    device_sn = request.args.get('deviceSn')

    personService.set_username_to_device(device_sn)

    return jsons.dump(Msg.success())

@app.route('/senduser_to_device', methods=['POST', 'GET'])
def send_user_to_device():
    if request.method == 'POST':
        data = request.get_json()
        enroll_id = data.get('enrollId')
        backupnum = data.get('backupNum', -1)
        device_sn = data.get('deviceSn')
        person = select_person_by_id(enroll_id)
        enroll_info = selectByBackupnum(enroll_id, backupnum)

        if enroll_info is not None:
            personService.set_user_to_device(enroll_id, person.name, backupnum, person.roll_id, enroll_info.signatures,device_sn)
            return jsons.dump(Msg.success())
        elif backupnum == -1:
            personService.set_user_to_device(enroll_id, person.name, backupnum, 0, "", device_sn)
            return jsons.dump(Msg.success())
        else:
            return jsons.dump(Msg.fail())
    else:
        return jsonify({"message": "get request accepted"})

@app.route('/senduser_info_to_device', methods=['GET', 'POST'])
def senduser_info_to_device():
    enroll_id = request.args.get('enrollId', type=int)
    backupnum = request.args.get('backupNum', type=int)
    device_sn = request.args.get('deviceSn')
    person = select_person_by_id(enroll_id)
    enroll_info = selectByBackupnum(enroll_id, backupnum)
    if enroll_info is not None:
        personService.set_user_to_device(enroll_id, person.name, backupnum, person.roll_id, enroll_info.signatures,device_sn)
        return jsons.dump(Msg.success())
    elif backupnum == -1:
        personService.set_user_to_device(enroll_id, person.name, backupnum, 0, "", device_sn)
        return jsons.dump(Msg.success())
    else:
        return jsons.dump(Msg.fail())


@app.route('/getDeviceInfo', methods=['GET']) #2024年1月8日18:06:22
def get_device_info():
    device_sn = request.args.get('deviceSn')

    message ='{"cmd":"getdevinfo"}'
    machine_command = MachineCommand(content=message, name="getdevinfo", status=0, send_status=0, err_count=0, serial = device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())


@app.route('/setOneUser', methods=['GET']) #2024年1月8日18:06:22
def set_one_user_to():
    enroll_id = request.args.get('enrollId', type=int)
    backupnum = request.args.get('backupNum', type=int)
    device_sn = request.args.get('deviceSn')
    app.logger.info(f"Set one user to device: {enroll_id}, {backupnum}, {device_sn}")

    # print("Set one user to device: ", enroll_id, backupnum, device_sn)
    person = select_person_by_id(enroll_id)
    app.logger.info(f"Person info: {person}")
    enroll_info = selectByBackupnum(enroll_id, backupnum)
    app.logger.info(f"Enroll info: {enroll_info}")
    # print("Enroll info: ", enroll_info)
    # print("Enroll info: ", enroll_info.signatures)
    if enroll_info is not None:
        try:
            result = personService.set_user_to_device(enroll_id, person.name, backupnum, person.roll_id, enroll_info.signatures,device_sn)
            app.logger.info(f"Result: {result}")
            return jsons.dump(Msg.success())
        except Exception as e:
            app.logger.exception(f"Error setting user to device: {str(e)}")
            return jsons.dump(Msg.fail())
    elif backupnum == -1:
        try:
            result1 = personService.set_user_to_device(enroll_id, person.name, backupnum, 0, "", device_sn)
            app.logger.info(f"Result: {result1}")
            return jsons.dump(Msg.success())
        except Exception as e:
            app.logger.exception(f"Error setting user to device: {str(e)}")
            return jsons.dump(Msg.fail())
    else:
        return jsons.dump(Msg.fail())

@app.route('/setOneUserJson', methods=['POST'])
def set_one_user_json():
    """Send users to the device.
    Description:
        This function sends user information to the device.
        payload: {
            enrollId: int: The user's ID.
            backupNum: int: The backup number.
            deviceSn: str: The device serial number.
        }
        Returns:
            JSON: A JSON response.
    """
    try:
        data = request.get_json()
        app.logger.info(f"Received JSON: {data}")

        if not data:
            app.logger.warning("Invalid JSON data received.")
            return jsons.dump(Msg.fail())

        enroll_id = data.get('enrollId')
        backupnum = data.get('backupNum')
        device_sn = data.get('deviceSn')
        app.logger.info(f"Parameters: {enroll_id}, {backupnum}, {device_sn}")

        #Get the database_name given the device_sn
        try:
            database_name = CompanyDevice.get_database_name_by_sn(device_sn)
            app.logger.info(f"Database name: {database_name}")
        except Exception as e:
            app.logger.error(f"Error getting database name: {str(e)}")
            return jsons.dump(Msg.fail())

        # Connect to the database
        with db_connection.get_session(database_name) as session:
            if enroll_id is None or backupnum is None or device_sn is None:
                app.logger.warning("Missing required parameters.")
                return jsons.dump(Msg.fail())

            person = select_person_by_id(session, enroll_id)
            app.logger.info(f"Person info: {person}")
            enroll_info = selectByBackupnum(session, enroll_id, backupnum)
            app.logger.info(f"Enroll info: {enroll_info}")

            if enroll_info is not None:
                app.logger.info(f"Sending user {enroll_id} to device {device_sn}")
                sent = personService.set_user_to_device(
                    session, enroll_id, person.name, backupnum, person.roll_id, enroll_info.signatures, device_sn
                )
                app.logger.info(f"information sent: {sent}")
                if sent:
                    return jsons.dump(Msg.success())
                else:
                    return jsons.dump(Msg.fail())
            elif backupnum == -1:
                app.logger.info(f"Sending user {enroll_id} to device {device_sn} with backupnum -1")
                personService.set_user_to_device(
                    session, enroll_id, person.name, backupnum, 0, "", device_sn
                )
                return jsons.dump(Msg.success())
            else:
                app.logger.error(f"Enroll info not found for enroll_id={enroll_id}, backupnum={backupnum}")
                return jsons.dump(Msg.fail())

    except Exception as e:
        app.logger.exception(f"Error processing request: {str(e)}")
        return jsons.dump(Msg.fail())

@app.route('/deletePersonFromDevice', methods=['GET'])
def delete_device_user_info():
    enroll_id = request.args.get('enrollId', type=int)
    device_sn = request.args.get('deviceSn')

    print("Deleting user devicesn: ", device_sn)
    personService.delete_user_info_from_device(enroll_id, device_sn)

    return jsons.dump(Msg.success())

@app.route('/getAllLog', methods=['GET'])
def getAllLog():
    device_sn = request.args.get('deviceSn')

    message = '{"cmd":"getalllog","stn":true}'
    # messageTemp = '{"cmd":"getalllog","stn":true,"from":"2020-12-03","to":"2020-12-30"}'

    machine_command = MachineCommand()
    machine_command.content = message
    machine_command.name = "getalllog"
    machine_command.status = 0
    machine_command.send_status = 0
    machine_command.err_count = 0
    machine_command.serial = device_sn
    machine_command.gmt_crate = datetime.now()
    machine_command.gmt_modified = datetime.now()

    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())

@app.route('/getNewLog', methods=['GET'])
def get_new_log():
    device_sn = request.args.get('deviceSn')
    message = '{"cmd": "getnewlog", "stn": true}'
    machine_command = MachineCommand(content=message, name="getnewlog", status=0, send_status=0, err_count=0,serial=device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())

from  application.Services.AccessDayService import  AccessDayService
@app.route('/setAccessDay', methods=['POST'])
def set_access_day():
    access_day = request.form.to_dict()
    print(access_day)
    if get_access_day_by_id(access_day['id']) is not None:
        return jsons.dump(Msg.fail())
    access_day_instance = AccessDay(**access_day)
    db.session.add(access_day_instance)
    # insert_access_day(access_day)
    accessDayService=AccessDayService()
    accessDayService.set_access_day()
    return jsons.dump(Msg.success())


@app.route('/setAccessWeek', methods=['POST'])
def set_access_week():
    access_week = request.form.to_dict()  # assumes you're receiving JSON data in the request body
    if get_access_week_by_id(access_week['id']) is not None:
        return jsons.dump(Msg.fail())
    access_week_instance = AccessWeek(**access_week)
    db.session.add(access_week_instance)
    accessWeekService = AccessWeekService()
    accessWeekService.set_access_week()
    return jsons.dump(Msg.success())

from flask import send_from_directory  #2024年1月10日23:37:16
@app.route('/img/<filename>', methods=['GET'])
def upload_file(filename):
    path = readConf().GetUploadParam()
    return send_from_directory(path, filename)

@app.route('/setLocckGroup', methods=['POST'])
def set_lock_group():
    lock_group =  request.form.to_dict()
    lockGroupService = LockGroupService()
    lockGroupService.set_lock_group(lock_group)
    return jsons.dump(Msg.success())

@app.route('/setUserLock', methods=['POST'])  #2024年1月10日23:37:16
def set_user_lock():
    user_lock =  request.form.to_dict()
    userLockService = UserLockService()
    userLockService.set_user_lock(user_lock, user_lock['starttime'], user_lock['endtime'])
    return jsons.dump(Msg.success())

from application.Models.Page import PageInfo
@app.route('/emps', methods=['GET'])
def get_all_person_from_db():
    pn = request.args.get('pn', default=1, type=int)
    person_list = select_all()
    enroll_list = get_all_enroll_info()  #EnrollInfo.select_all()
    emps = []
    for person in person_list:
        for enroll_info in enroll_list:
            if person.id == enroll_info.enroll_id and enroll_info.backupnum == 50:
                emps.append({
                    'enrollId': person.id,
                    'admin': person.roll_id,
                    'name': person.name,
                    'imagePath': enroll_info.imagepath
                })
    page=PageInfo(emps,5)
    # return jsons.dump(success=True, pageInfo={'emps': emps, 'pn': pn})  # you'll need to implement your own paging
    return jsons.dump(Msg.success().add("pageInfo", page))
#Msg.success().add("device", device_list)
@app.route('/records', methods=['GET'])
def get_all_log_from_db():
    """Get all recordes from the database."""
    pn = request.args.get('pn', default=1, type=int)
    records = select_all_records()

    records = [record.to_dict() for record in records]
    print(records)
    app.logger.info(f"Records: {records}")
    pageInfo=PageInfo(records,5)
    # print(pageInfo)
    return jsons.dump(Msg.success().add("pageInfo", pageInfo))

@app.route('/daily_records', methods=['GET'])
def daily_records():
    """Get daily records."""
    today_date = datetime.now().date()  # Extract only the date part

    # Get the company_id from the request parameters
    company_id = request.args.get('company_id')
    if not company_id:
        app.logger.error("Missing company_id parameter")
        return jsons.dump(Msg.fail().set_message("Missing company_id parameter"))

    app.logger.info(f"Company ID: {company_id}")

    # Get the database name from the company_id
    try:
        database_name = Company.get_database_given_company_id(company_id)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        return jsons.dump(Msg.fail().set_message("Invalid company ID"))

    # Connect to the database
    with db_connection.get_session(database_name) as session:
        try:
            records = Record.get_daily_records(session, today_date)
            if not records:
                app.logger.info("No daily records found")
                return jsons.dump(Msg.success().add("records", []))  # Return empty list instead of failure

            records = [record.to_dict(session) for record in records]
            app.logger.info(f"Daily records: {records}")
            return jsons.dump(Msg.success().add("records", records))
        except Exception as e:
            app.logger.error(f"Error getting daily records: {str(e)}")
            return jsons.dump(Msg.fail().set_message("Error retrieving records"))

@app.route('/update_person', methods=['POST'])
def update_person():
    person = request.get_json()
    person_id = person.get('id')
    employee_id = person.get('employee_id')
    app.logger.info(f"Person: {person}")
    app.logger.info(f"Person id: {person_id}")
    app.logger.info(f"Employee id: {employee_id} and the type is {type(employee_id)}")
    if not person_id:
        message = "Id is required."
        return jsons.dump(Msg.fail().add("message", message))
    if not employee_id:
        employee_id = None
    try:
        update = Person.update_person(person_id, employee_id)
        app.logger.info(f"Person updated: {update}")
        return jsons.dump(Msg.success())
    except Exception as e:
        app.logger.error(f"Error updating person: {str(e)}")
        return jsons.dump(Msg.fail())

@app.route('/get_persons', methods=['GET'])
def get_persons():
    persons = Person.get_persons()
    persons = [person.to_dict() for person in persons]
    app.logger.info(f"Persons: {persons}")
    return jsons.dump(Msg.success().add("persons", persons))

@app.route('/accessDays', methods=['GET'])
def get_access_day_from_db():
    access_days = get_all_access_days()
    access_days = [access_day.to_dict() for access_day in access_days]  # Convert each Device to a dictionary
    # print(access_days)
    return jsons.dump(Msg.success().add("accessdays", access_days))



@app.route('/uploadUserToDevice', methods=['POST']) #todo:这个可能没有用 2024年1月11日09:57:54
def upload_user_to_device():
    enroll_id = request.args.get('enrollId', type=int)
    person = Person.selectByPrimaryKey(enroll_id)
    # you'll need to implement the actual upload function
    return jsons.dump(Msg.success())

@app.route('/openDoor', methods=['GET'])
def open_door():
    door_num = request.args.get('doorNum', type=int)
    device_sn = request.args.get('deviceSn')
    message = json.dumps({'cmd': 'opendoor', 'doornum': door_num})
    machine_command = MachineCommand(content=message, name="opendoor", status=0, send_status=0, err_count=0,serial=device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())


@app.route('/getDevLock', methods=['GET'])
def get_dev_lock():
    device_sn = request.args.get('deviceSn')
    message =json.dumps({"cmd": "getdevlock"})
    machine_command = MachineCommand(content=message, name="getdevlock", status=0, send_status=0, err_count=0,serial=device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())


@app.route('/getUserLock', methods=['GET'])
def get_user_lock():
    enroll_id = request.args.get('enrollId', type=int)
    device_sn = request.args.get('deviceSn')
    message = json.dumps({"cmd": "getuserlock", "enrollid": enroll_id})
    machine_command = MachineCommand(content=message, name="getuserlock", status=0, send_status=0, err_count=0, serial=device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())


@app.route('/cleanAdmin', methods=['GET'])
def clean_admin():
    device_sn = request.args.get('deviceSn')
    message =json.dumps({"cmd": "cleanadmin"})
    machine_command = MachineCommand(content=message, name="cleanadmin", status=0, send_status=0, err_count=0, serial=device_sn)
    machine_command.insert_machine_command(machine_command)
    return jsons.dump(Msg.success())

#endregion-----------web 处理结束---------------------------------------------

#region-----------web socket处理开始---------------------------------------------
@sock.route('/')
def websock(sock):
    while True:
        data = sock.receive()
        #if not data is None:
        app.logger.info(f"Data received: {data}")
        # sock.send(data[::-1])
        #sock.send(data)

@sock.route('/pub/chat')
def handler(sock):
    device_sn = None  # Track device serial number for logging
    try:
        while True:
            message = sock.receive()
            log_websocket_activity(device_sn, "message_received", f"Length: {len(message)}")

            try:
                # Parse JSON message
                try:
                    jsonMsg = json.loads(message)
                    # Extract device serial number for better logging
                    device_sn = jsonMsg.get("sn", device_sn)
                except Exception as e:
                    log_device_error("Failed to parse JSON message", device_sn, e)
                    continue

                try:
                    cmd = jsonMsg.get("cmd", "")
                except Exception as err:
                    log_device_error("Error getting cmd from message", device_sn, err)
                    cmd = ""

                try:
                    ret = jsonMsg.get("ret", "")
                except Exception as err:
                    log_device_error("Error getting ret from message", device_sn, err)
                    ret = ""

                if len(cmd) != 0:  # client active send data
                    log_device_info(f"Processing command: {cmd}", device_sn)

                    if cmd == "reg":
                        log_device_info(f"Device registration request", device_sn)
                        try:
                            device_info = get_device_info_websocket(jsonMsg, sock)
                            log_device_info(f"Device registration completed", device_sn)
                        except Exception as err:
                            log_device_error("Error during device registration", device_sn, err)
                            sock.send("{\"ret\":\"reg\",\"result\":false,\"reason\":1}")
                        """try:
                            # Send request to get user list
                            app.logger.info(f"getting user list")
                            get_userlist_command = json.dumps({"cmd": "getuserlist", "stn": True})
                            app.logger.info(f"Sending getuserlist command: {get_userlist_command}")
                            sent = sock.send(get_userlist_command)
                            app.logger.info(f"Command sent: {sent}")
                        except Exception as err:
                            app.logger.error(f"Error sending getuserlist command: {str(err)}")
                            import traceback
                            traceback.print_exc()
                            # sock.send(json.dumps({"ret": "reg", "result": False, "reason": 1}))
                            sock.send("{\"ret\":\"reg\",\"result\":false,\"reason\":1}")
                        """
                    elif cmd == "sendlog":
                        try:
                            # print("sendlog:" + str(jsonMsg))
                            get_attendance(jsonMsg, sock)
                        except Exception as err:
                            print(err)
                            import traceback
                            traceback.print_exc()
                            #sock.send(json.dumps({"ret": "sendlog", "result": False, "reason": 1}))
                            sock.send("{\"ret\":\"sendlog\",\"result\":false,\"reason\":1}")
                    elif cmd == "senduser":
                        try:
                            get_enroll_info(jsonMsg, sock)
                        except Exception as err:
                            print(err)
                            import traceback
                            traceback.print_exc()
                            # sock.send(json.dumps({"ret": "senduser", "result": False, "reason": 1}))
                            sock.send("{\"ret\":\"senduser\",\"result\":false,\"reason\":1}")
                    else:
                        print("cmd未知:" + cmd)
                elif len(ret) != 0 :  # server send cmd and rec data
                    print("ret--:" + ret)
                    if ret == "getuserlist":
                        print("getuserlist:" + str(jsonMsg))
                        get_user_list(jsonMsg, sock)
                    elif ret == "getuserinfo":
                        print("getuserinfo:" + str(jsonMsg))
                        get_user_info_websocket(jsonMsg, sock)
                        sn = jsonMsg["sn"]
                        deviceStatus=DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)

                    elif ret == "setuserinfo":
                        print("下发数据" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn,"setuserinfo")
                    elif ret == "getalllog":
                        print("获取所有打卡记录"+ str(jsonMsg))
                        try:
                            get_all_log(jsonMsg, sock)
                        except Exception as err:
                            import traceback
                            traceback.print_exc()
                            print(err)
                    elif ret == "getnewlog":

                        print("获取所有打卡记录 getnewlog" + str(jsonMsg))
                        try:
                            get_new_log(jsonMsg, sock)
                        except Exception as err:
                            import traceback
                            traceback.print_exc()
                            print(err)

                    elif ret == "deleteuser":
                        print("删除人员" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "deleteuser")
                    elif ret == "initsys":
                        print("初始化系统" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "initsys")
                    elif ret == "setdevlock":
                        print("设置天时间段" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "setdevlock")
                    elif ret == "setuserlock":
                        print("门禁授权" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "setuserlock")

                    elif ret == "getdevinfo":
                        print("设备信息" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "getdevinfo")
                    elif ret == "setusername":
                        print("下发姓名" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "setusername")

                    elif ret == "reboot":
                        print("reboot:" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "reboot")

                    elif ret == "getdevlock":
                        print("getdevlock:" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "getdevlock")

                    elif ret == "getuserlock":
                        print("getuserlock:" + str(jsonMsg))
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, "getuserlock")

                    else:
                        print("未知命令:" + ret)
                        sn = jsonMsg["sn"]
                        deviceStatus = DeviceStatus()
                        deviceStatus.device_sn = sn
                        deviceStatus.websocket = sock
                        deviceStatus.status = 1
                        update_device_websocket(sn, deviceStatus)
                        update_command_status_websocket(sn, ret)

            except Exception as ex:
                print("error:" + str(ex))
                import traceback
                traceback.print_exc()
                # Logger.logr.error(ex)

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(e)

    finally:
        pass
#region 功能函数
from application.Models.DeviceStatus import DeviceStatus
from application.web_socket.WebSocketPool import WebSocketPool
def update_device_websocket(sn, device_status):

    if WebSocketPool.get_device_status(sn) is not None:
        WebSocketPool.add_device_and_status(sn, device_status)
    else:
        WebSocketPool.add_device_and_status(sn, device_status)

def update_command_status_websocket(serial, command_type):
    """Update the command status."""
    log_device_info(f"Updating command status for command: {command_type}", serial)

    # connect to the right database
    try:
        database_name = CompanyDevice.get_database_name_by_sn(serial)
        device_logger.log_database_activity(serial, database_name, "command_status_update")
    except Exception as e:
        log_device_error("Error getting database name for command status update", serial, e)
        database_name = None
        return

    with db_connection.get_session(database_name) as session:
        try:
            machine_command = find_pending_command(session, 1, serial)
            if len(machine_command) > 0 and machine_command[0].name == command_type:
                log_device_info(f"Found matching command to update: {command_type}", serial)
                try:
                    result = update_command_status(session, 1, 0, datetime.now(), machine_command[0].id)
                    log_device_info(f"Command status updated successfully: {command_type}", serial)
                except Exception as e:
                    log_device_error(f"Error updating command status for {command_type}", serial, e)
            else:
                log_device_warning(f"No matching command found for {command_type}", serial)
        except Exception as e:
            log_device_error(f"Error finding pending command for {command_type}", serial, e)

from application.Models.Device import Device,get_device_by_serial_num,insert_device,update_status_by_primary_key

time_stamp =0
time_stamp2=0
def get_device_info_websocket(json_node, sock_):
    app.logger.info(f"Get device info: {json_node}")
    from application.utils.db_connection import DatabaseConnection
    from application.Models.company import Company

    # Initialize the connection
    db_connection = DatabaseConnection()
    sn = json_node.get('sn')
    app.logger.info(f"Device serial number: {sn}")
    try:
        app.logger.info(f"Getting database name for device: {sn}")
        database_name = CompanyDevice.get_database_name_by_sn(sn)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")

    device_status = {}

    if not database_name:
        app.logger.warning(f"Device {sn} is not registered to any company.")
        message = json.dumps({'ret': 'reg', 'result': False, 'reason': 2})
        sock_.send(message)
        return

    if sn:
        # connect to the right database
        with db_connection.get_session(database_name) as session:

            d1 = get_device_by_serial_num(session, sn)
            app.logger.info(f"Device details: {d1}")

            # print(d1.serial_num)
            if d1 is None:
                i = insert_device(session, sn, 1)
                app.logger.info(f"Device {sn} inserted: {i}")
            else:
                update_status_by_primary_key(session, d1.id, 1)

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        message = json.dumps({'ret': 'reg', 'result': True, 'cloudtime': current_time})
        # emit('message', message, room=args1)
        sock_.send(message)
        device_status = {'websocket': sock_, 'status': 1, 'device_sn': sn}
        update_device_websocket(sn, device_status)
        # print(cls.get_device_status(sn))
        # j_obj = {'SN': sn, 'currentTime': datetime.now().timestamp()}
        # RestTemplateUtil.post_device_info(j_obj)  # Implement this method in your way
    else:
        message = json.dumps({'ret': 'reg', 'result': False, 'reason': 1})
        # emit('message', message, room=args1)
        sock_.send(message)
        device_status = {'websocket': sock_, 'status': 1, 'device_sn': sn}
        update_device_websocket(sn, device_status)
    global time_stamp
    global time_stamp2
    time_stamp = datetime.now().timestamp()
    time_stamp2 = time_stamp
    #endregion-----------web socket处理结束---------------------------------------------


import uuid

# from collections import defaultdict
from application.Models.Records import Record,insert_record,select_all_records,select_record_by_id,update_record_by_id

def get_attendance(json_node, conn):
    sn = json_node["sn"]
    count = json_node["count"]
    log_index = json_node.get("logindex", -1)
    record_all = []
    device_status = DeviceStatus()
    flag = False
    if count > 0:
        for record in json_node["record"]:
            obj = {} # todo: 现在没有用，这个 2024年1月11日18:40:14
            enroll_id = record["enrollid"]
            time_str = record["time"]
            mode = record["mode"]
            in_out = record["inout"]
            event = record["event"]
            temperature = 0
            if record.get("temp"):
                temperature = round(record["temp"] / 10, 1)
                obj["temperature"] = str(temperature)
            records = {
                'device_serial_num': sn,
                'enroll_id': enroll_id,
                'event': event,
                'intOut': in_out,
                'mode': mode,
                'records_time': time_str,
                'temperature': temperature
            }
            if enroll_id == 99999999:
                obj["resultStatus"] = 0
            else:
                obj["resultStatus"] = 1
            obj["IdentifyType"] = "0"
            obj["SN"] = sn
            try:
                if record["image"]:
                    pic_name = str(uuid.uuid4())
                    obj["face_base64"] = record["image"]
                    flag = base64_to_image(record["image"], pic_name)
                    # if flag:
                    records["image"] = pic_name + ".jpg"
            except:
                pass
            print("records====1")
            print(records)
            record_all.append(records)
            obj["time"] = time_str
            obj["userid"] = str(enroll_id)
            # Rest of your logic here
        if log_index >= 0:
            conn.send(json.dumps({"ret":"sendlog","result":True,"count":count,"logindex":log_index,"cloudtime":str(datetime.now())}))
        elif log_index < 0:
            conn.send(json.dumps({"ret":"sendlog","result":True,"cloudtime":str(datetime.now())}))
        device_status.websocket = conn
        device_status.status = 1
        device_status.device_sn = sn
        update_device_websocket(sn, device_status)
    elif count == 0:
        conn.send(json.dumps({"ret":"sendlog","result":False,"reason":1}))
        device_status.websocket = conn
        device_status.status = 1
        device_status.device_sn = sn
        update_device_websocket(sn, device_status)
    app.logger.info(f"Records accessible: {record_all}")
    # Get the database name
    try:
        database_name = CompanyDevice.get_database_name_by_sn(sn)
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        database_name = None
    # connect to the right database
    with db_connection.get_session(database_name) as session:
          app.logger.info("inserting the records")
          for record in record_all:
            try:
                # Insert the record into the records table
                inserted = insert_record2(session, **record) # dict 保存 2024年1月22日13:25:02
                app.logger.info(f"Record inserted: {inserted}")
                app.logger.info(f"Record data: {record}")

                # Process the record for attendance tracking
                try:
                    from application.Models.employees.attendance import Attendance
                    app.logger.info("About to process attendance from record")
                    attendance_result = Attendance.process_attendance_from_record(session, record)
                    app.logger.info(f"Attendance result: {attendance_result}")

                    if attendance_result.get("success"):
                        app.logger.info(f"Attendance processed successfully: {attendance_result.get('message')}")
                    else:
                        app.logger.warning(f"Attendance processing issue: {attendance_result.get('message')}")
                except Exception as e:
                    app.logger.error(f"Error in attendance processing: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())

            except Exception as e:
                app.logger.error(f"Error processing record: {str(e)}")
                import traceback
                app.logger.error(traceback.format_exc())
    global timestamp2
    timestamp2 = datetime.now()

def base64_to_image(base64_string, pic_name):
    try:
        image_data = base64.b64decode(base64_string)
        with open(os.path.join(readConf_.GetUploadParam(), pic_name + '.jpg'), 'wb') as f:
            f.write(image_data)
        return True
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

import json

def get_enroll_info(json_node, conn):

    # enroll_id = json_node["enrollid"]
    sn = json_node["sn"]
    signatures1 = json_node["record"]
    flag = False
    device_status = DeviceStatus()

    if signatures1 is None:
        conn.send(json.dumps({"ret": "senduser", "result": False, "reason": 1}))
        device_status.websocket = conn
        device_status.status = 1
        device_status.device_sn = sn
        update_device_websocket(sn, device_status)
    else:
        backupnum = json_node["backupnum"]
        enroll_id = json_node["enrollid"]
        name = json_node["name"]
        roll_id = json_node["admin"]
        signatures = json_node["record"]
        app.logger.info(f"Enroll info: {enroll_id}, {backupnum}, {name}, {roll_id}, {signatures}")

        # Generate a new UUID for the employee_id
        employee_id = str(uuid.uuid4())
        app.logger.info(f"Generated employee_id: {employee_id}")

        person = {
            'id': enroll_id,
            'name': name,
            'roll_id': roll_id,
            'employee_id': employee_id  # Include the employee_id
        }
        app.logger.info(f"Person to be inserted: {person}")

        # Get the database name
        try:
            database_name = CompanyDevice.get_database_name_by_sn(sn)
        except Exception as e:
            app.logger.error(f"Error getting database name: {str(e)}")
            database_name = None

        # connect to the right database
        with db_connection.get_session(database_name) as session:
            # check if the person exists
            person_lookup = select_person_by_id(session, enroll_id)
            if person_lookup is None:
                inserted = insert_person(session, **person)
                app.logger.info(f"Person inserted: {inserted}")

                # Create an Employee record with the same employee_id
                try:
                    from application.Models.employees.employee import Employee

                    # Parse name into first and last name
                    name_parts = name.split(' ', 1)
                    first_name = name_parts[0]
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    # Create employee data
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': first_name,
                        'last_name': last_name,
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for {name} with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for {name}")
                except Exception as e:
                    app.logger.error(f"Error creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            elif person_lookup.employee_id is None:
                # If person exists but doesn't have an employee_id, update it and create an Employee record
                try:
                    # Update the person with the employee_id
                    person_lookup.employee_id = employee_id
                    session.commit()
                    app.logger.info(f"Updated existing person with employee_id: {employee_id}")

                    # Create an Employee record
                    from application.Models.employees.employee import Employee

                    # Parse name into first and last name
                    name_parts = name.split(' ', 1)
                    first_name = name_parts[0]
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    # Create employee data
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': first_name,
                        'last_name': last_name,
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for existing person {name} with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for existing person {name}")
                except Exception as e:
                    app.logger.error(f"Error updating person or creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            else:
                # Person exists and has an employee_id, check if Employee record exists
                try:
                    from application.Models.employees.employee import Employee

                    # Check if employee record exists
                    existing_employee = Employee.get_employee_by_id(session, uuid.UUID(person_lookup.employee_id))

                    if not existing_employee:
                        # Create an Employee record with the existing employee_id
                        name_parts = name.split(' ', 1)
                        first_name = name_parts[0]
                        last_name = name_parts[1] if len(name_parts) > 1 else ""

                        employee_data = {
                            'employee_id': uuid.UUID(person_lookup.employee_id),
                            'first_name': first_name,
                            'last_name': last_name,
                            'status': 'active'
                        }

                        new_employee = Employee.create_employee(session, **employee_data)

                        if new_employee:
                            app.logger.info(f"Created new employee record for person with existing employee_id {person_lookup.employee_id}")
                        else:
                            app.logger.error(f"Failed to create employee record for person with existing employee_id {person_lookup.employee_id}")
                except Exception as e:
                    app.logger.error(f"Error checking or creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())

            enroll_info = {
                'enroll_id': enroll_id,
                'backupnum': backupnum,
                'signatures': signatures
            }

            if backupnum == 50:
                pic_name = str(uuid.uuid4())
                flag = base64_to_image(json_node["record"], pic_name)
                enroll_info["imagepath"] = pic_name + ".jpg"

            if selectByBackupnum(session, enroll_id, backupnum) is None:
                insert_enroll_info(session, **enroll_info)
            else:
                update_enroll_info2(session, enroll_id, enroll_info["imagepath"], enroll_info["signatures"]) # 更新，2024年1月25日10:31:19 新加

            conn.send(json.dumps(
                {"ret": "senduser", "result": True, "cloudtime": datetime.now().strftime('%Y-%m-%d %H:%M:%S')}))
            device_status.websocket = conn
            device_status.status = 1
            device_status.device_sn = sn
            update_device_websocket(sn, device_status)

    global timestamp2
    timestamp2 = datetime.now()


from  application.Models.UserTemp import UserTemp
def get_user_list(json_node, conn):
    app.logger.info(f"Received user list response: {json_node}")
    user_temps = []

    result = json_node["result"]
    sn = json_node["sn"]
    device_status = DeviceStatus()
    app.logger.info(f"result from the device: {result} and the sn is {sn} with a status of {device_status.status}")

    if result:
        count = json_node["count"]
        records = json_node["record"]
        print("get_user_list:count "+str(count))
        app.logger.info(f"records retrieved from device: {records}")
        if count > 0:
            for record in records:
                enroll_id = record["enrollid"]
                admin = record["admin"]
                backupnum = record["backupnum"]
                user_temp = UserTemp(enrollId = enroll_id, backupnum=backupnum, admin=admin)
                user_temps.append(user_temp)

            # conn.send(json.dumps({"cmd": "getuserlist", "stn": False}))
            conn.send("{\"cmd\":\"getuserlist\",\"stn\":false}")
            print("get_user_list count > 0 send")
            print(conn)
            device_status.websocket = conn
            device_status.status = 1
            device_status.device_sn = sn
            update_device_websocket(sn, device_status)

    # get the database to connect to
    try:
        database_name = CompanyDevice.get_database_name_by_sn(sn)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        database_name = None

    # connect to the right database
    with db_connection.get_session(database_name) as session:

        for user_temp in user_temps:
            if select_person_by_id(session, user_temp.enrollId) is None:
                # Generate a new UUID for the employee_id
                employee_id = str(uuid.uuid4())
                app.logger.info(f"Generated employee_id: {employee_id}")

                # person_temp = Person(id=user_temp.enrollId, name="", roll_id=user_temp.admin)
                person_temp={
                    'id': user_temp.enrollId,
                    'name': "",
                    'roll_id': user_temp.admin,
                    'employee_id': employee_id  # Include the employee_id
                }
                inserted = insert_person(session, **person_temp)
                app.logger.info(f"Person inserted: {inserted}")

                # Create an Employee record with the same employee_id
                try:
                    from application.Models.employees.employee import Employee

                    # Create employee data with default name (can be updated later)
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': "Unknown",
                        'last_name': "",
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for user_temp with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for user_temp")
                except Exception as e:
                    app.logger.error(f"Error creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            elif select_person_by_id(session, user_temp.enrollId).employee_id is None:
                # If person exists but doesn't have an employee_id, update it and create an Employee record
                try:
                    # Generate a new UUID for the employee_id
                    employee_id = str(uuid.uuid4())
                    app.logger.info(f"Generated employee_id: {employee_id}")

                    # Update the person with the employee_id
                    person = select_person_by_id(session, user_temp.enrollId)
                    person.employee_id = employee_id
                    session.commit()
                    app.logger.info(f"Updated existing person with employee_id: {employee_id}")

                    # Create an Employee record
                    from application.Models.employees.employee import Employee

                    # Create employee data with default name (can be updated later)
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': "Unknown",
                        'last_name': "",
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for existing person with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for existing person")
                except Exception as e:
                    app.logger.error(f"Error updating person or creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            else:
                # Person exists and has an employee_id, check if Employee record exists
                try:
                    from application.Models.employees.employee import Employee

                    person = select_person_by_id(session, user_temp.enrollId)
                    # Check if employee record exists
                    existing_employee = Employee.get_employee_by_id(session, uuid.UUID(person.employee_id))

                    if not existing_employee:
                        # Create an Employee record with the existing employee_id
                        employee_data = {
                            'employee_id': uuid.UUID(person.employee_id),
                            'first_name': "Unknown",
                            'last_name': "",
                            'status': 'active'
                        }

                        new_employee = Employee.create_employee(session, **employee_data)

                        if new_employee:
                            app.logger.info(f"Created new employee record for person with existing employee_id {person.employee_id}")
                        else:
                            app.logger.error(f"Failed to create employee record for person with existing employee_id {person.employee_id}")
                except Exception as e:
                    app.logger.error(f"Error checking or creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())

        for user_temp in user_temps:
            if selectByBackupnum(session, user_temp.enrollId, user_temp.backupnum) is None:
                # enroll_info = EnrollInfo(enroll_id=user_temp.enrollId, backupnum=user_temp.backupnum)
                enroll_info={
                    'enroll_id': user_temp.enrollId,
                    'backupnum': user_temp.backupnum,
                    'imagepath':  "",
                    'signatures': ""
                }
                try:
                    inserted = insert_enroll_info(session, **enroll_info)
                    app.logger.info(f"Enroll info inserted: {inserted}")
                except Exception as e:
                    app.logger.error(f"Error inserting enroll info: {str(e)}")

    update_command_status_websocket(sn, "getuserlist")
    print("update_command_status_websocket")
from application.Models.MachineCommand import MachineCommand,find_pending_command,update_command_status
# def update_command_status_(serial, command_type):
#     machine_command = find_pending_command(1, serial)
#     if len(machine_command) > 0 and machine_command[0].name == command_type:
#         update_command_status(1, 0, datetime.datetime.now(), machine_command[0].id)

from application.Models.Person import insert_person2,update_by_primary_key
def get_user_info_websocket(json_node, conn):
    result = json_node["result"]
    sn = json_node["sn"]
    flag = False

    # Get the database name for the device
    try:
        database_name = CompanyDevice.get_database_name_by_sn(sn)
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        database_name = None

    if result:
        backupnum = json_node["backupnum"]
        signatures1 = json_node["record"]
        enroll_id = json_node["enrollid"]
        name = json_node["name"]
        admin = json_node["admin"]
        signatures = json_node["record"]

        # Generate a new UUID for the employee_id
        employee_id = str(uuid.uuid4())
        app.logger.info(f"Generated employee_id: {employee_id}")

        # connect to the right database
        with db_connection.get_session(database_name) as session:
            enroll_info = selectByBackupnum(session, enroll_id, backupnum)

            if backupnum == 50:
                pic_name = str(uuid.uuid4())
                flag = base64_to_image(json_node["record"], pic_name)
                if enroll_info:
                    enroll_info.imagepath = pic_name + ".jpg"

            existing_person = select_person_by_id(session, enroll_id)
            if existing_person is None:
                # Create a new person with employee_id
                person = Person(id=enroll_id, name=name, roll_id=admin, employee_id=employee_id)
                insert_person2(session, person)
                app.logger.info(f"Person inserted with employee_id: {employee_id}")

                # Create an Employee record with the same employee_id
                try:
                    from application.Models.employees.employee import Employee

                    # Parse name into first and last name
                    name_parts = name.split(' ', 1)
                    first_name = name_parts[0]
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    # Create employee data
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': first_name,
                        'last_name': last_name,
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for {name} with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for {name}")
                except Exception as e:
                    app.logger.error(f"Error creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            elif existing_person.employee_id is None:
                # Update existing person with employee_id and name/roll_id
                existing_person.name = name
                existing_person.roll_id = admin
                existing_person.employee_id = employee_id
                session.commit()
                app.logger.info(f"Updated existing person with employee_id: {employee_id}")

                # Create an Employee record
                try:
                    from application.Models.employees.employee import Employee

                    # Parse name into first and last name
                    name_parts = name.split(' ', 1)
                    first_name = name_parts[0]
                    last_name = name_parts[1] if len(name_parts) > 1 else ""

                    # Create employee data
                    employee_data = {
                        'employee_id': uuid.UUID(employee_id),  # Convert string to UUID
                        'first_name': first_name,
                        'last_name': last_name,
                        'status': 'active'
                    }

                    # Create the employee record
                    new_employee = Employee.create_employee(session, **employee_data)

                    if new_employee:
                        app.logger.info(f"Created new employee record for existing person {name} with ID {employee_id}")
                    else:
                        app.logger.error(f"Failed to create employee record for existing person {name}")
                except Exception as e:
                    app.logger.error(f"Error creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())
            else:
                # Update existing person with name/roll_id
                existing_person.name = name
                existing_person.roll_id = admin
                session.commit()
                app.logger.info(f"Updated existing person with name: {name}")

                # Check if Employee record exists
                try:
                    from application.Models.employees.employee import Employee

                    # Check if employee record exists
                    existing_employee = Employee.get_employee_by_id(session, uuid.UUID(existing_person.employee_id))

                    if not existing_employee:
                        # Create an Employee record with the existing employee_id
                        name_parts = name.split(' ', 1)
                        first_name = name_parts[0]
                        last_name = name_parts[1] if len(name_parts) > 1 else ""

                        employee_data = {
                            'employee_id': uuid.UUID(existing_person.employee_id),
                            'first_name': first_name,
                            'last_name': last_name,
                            'status': 'active'
                        }

                        new_employee = Employee.create_employee(session, **employee_data)

                        if new_employee:
                            app.logger.info(f"Created new employee record for person with existing employee_id {existing_person.employee_id}")
                        else:
                            app.logger.error(f"Failed to create employee record for person with existing employee_id {existing_person.employee_id}")
                except Exception as e:
                    app.logger.error(f"Error checking or creating employee record: {str(e)}")
                    import traceback
                    app.logger.error(traceback.format_exc())

            if enroll_info is None:
                enroll_info={
                    'enroll_id': enroll_id,  # Fixed from 0 to enroll_id
                    'backupnum': backupnum,  # Fixed from None to backupnum
                    'signatures': signatures,
                    'imagepath': pic_name + ".jpg" if backupnum == 50 and flag else None,
                }
                insert_enroll_info(session, **enroll_info)
            elif enroll_info is not None:
                from application.Models.EnrollInfo import update_by_primary_key_with_blobs
                enroll_info.signatures = signatures
                update_by_primary_key_with_blobs(session, enroll_info)

    update_command_status_websocket(sn, "getuserinfo")


def get_all_log(json_node, conn):
    result = json_node["result"]
    record_all = []
    sn = json_node["sn"]
    records = json_node["record"]
    device_status = DeviceStatus()
    flag = False

    if result:
        count = json_node["count"]
        if count > 0:
            for record in records:
                enroll_id = record["enrollid"]
                time_str = record["time"]
                mode = record["mode"]
                in_out = record["inout"]
                event = record["event"]
                temperature = 0.0
                if record.get("temp") is not None:
                    temperature = record["temp"]
                    temperature = temperature / 100
                    temperature = round(temperature, 1)

                rec = Record(enroll_id=enroll_id, event=event, intOut=in_out, mode=mode, records_time=time_str,
                              device_serial_num=sn, temperature=temperature)
                record_all.append(rec)

            conn.send(json.dumps({"cmd": "getalllog", "stn": False}))
            device_status.websocket = conn
            device_status.status = 1
            device_status.device_sn = sn
            update_device_websocket(sn, device_status)

    for record in record_all:
        insert_record(record)

    update_command_status_websocket(sn, "getalllog")


def get_all_log(json_node, conn):
    result = json_node["result"]
    record_all = []
    sn = json_node["sn"]
    records = json_node["record"]
    device_status = DeviceStatus()
    flag = False

    if result:
        count = json_node["count"]
        if count > 0:
            for record in records:
                enroll_id = record["enrollid"]
                time_str = record["time"]
                mode = record["mode"]
                in_out = record["inout"]
                event = record["event"]
                temperature = 0.0
                if record.get("temp") is not None:
                    temperature = record["temp"]
                    temperature = temperature / 100
                    temperature = round(temperature, 1)

                rec = Record(enroll_id=enroll_id, event=event, intOut=in_out, mode=mode, records_time=time_str,
                              device_serial_num=sn, temperature=temperature)
                record_all.append(rec)

            conn.send(json.dumps({"cmd": "getalllog", "stn": False}))
            device_status.websocket = conn
            device_status.status = 1
            device_status.device_sn = sn
            update_device_websocket(sn, device_status)

    for record in record_all:
        insert_record(record)

    update_command_status_websocket(sn, "getalllog")



def get_new_log(json_node, conn):
    result = json_node["result"]
    record_all = []
    sn = json_node["sn"]
    records = json_node["record"]
    device_status = DeviceStatus()
    flag = False

    if result:
        count = json_node["count"]
        if count > 0:
            for record in records:
                enroll_id = record["enrollid"]
                time_str = record["time"]
                mode = record["mode"]
                in_out = record["inout"]
                event = record["event"]
                temperature = 0.0
                if record.get("temp") is not None:
                    temperature = record["temp"]
                    temperature = temperature / 100
                    temperature = round(temperature, 1)

                rec = Record(enroll_id=enroll_id, event=event, intOut=in_out, mode=mode, records_time=time_str,
                              device_serial_num=sn, temperature=temperature)
                record_all.append(rec)

            conn.send(json.dumps({"cmd": "getnewlog", "stn": False}))
            device_status.websocket = conn
            device_status.status = 1
            device_status.device_sn = sn
            update_device_websocket(sn, device_status)

    for record in record_all:
        insert_record(record)(record)

        update_command_status_websocket(sn, "getnewlog")

#from application.Models.company import Company, CompanyDevice




# Import the Blueprint modules
from application.Routes.employees.employee import employee
from application.Routes.employees.employees_api import employees_api
from application.Routes.employees.announcements_api import announcements_api
from application.Routes.employees.company_users import company_users_bp
from application.Routes.company.company import company_bp
from application.Routes.system_settings.device_settings import device_settings_bp
from application.Routes.users.users import user
from application.Routes.dashboard.dashboard import dashboard_bp
from application.api_docs import api_doc
from application.sdk_generator import sdk_generator
from application.Routes.departments.departments import departments_api
from application.Routes.shifts.shifts import shifts_bp
from application.Routes.shifts.employee_shifts import employee_shifts_bp
from application.Routes.attendance1.attendance_api import attendance_api
from application.Routes.leave.leave_types_api import leave_types_api
from application.Routes.leave.leave_policies_api import leave_policies_api
from application.Routes.leave.leave_balances_api import leave_balances_api
from application.Routes.leave.leave_requests_api import leave_requests_api
from application.Routes.leave.leave_balance_doctor_api import leave_balance_doctor_api
from application.Routes.leave.leave_audit_api import leave_audit_api
from application.Routes.leave.leave_analytics_api import leave_analytics_api
from application.Routes.approval.approval_api import approval_api
from application.Routes.payroll.payroll_policies_api import payroll_policies_api
from application.Routes.payroll.employee_salary_api import employee_salary_api
from application.Routes.payroll.payroll_processing_api import payroll_processing_api
from application.Routes.payroll.payroll_settings_api import payroll_settings_api
from application.Routes.payroll.dynamic_payroll_api import dynamic_payroll_api
from application.Routes.company.country import countries_api
from application.Routes.payroll.policy_types_api import policy_types_api
from application.Routes.payroll.tax_brackets_api import tax_brackets_api
from application.Routes.payroll.deduction_types_api import deduction_types_api
from application.Routes.payroll.country_setup_api import country_setup_api

# Register the Blueprint modules
app.register_blueprint(employee)
app.register_blueprint(employees_api)
app.register_blueprint(announcements_api)
app.register_blueprint(company_users_bp)  # Register the company users blueprint
app.register_blueprint(company_bp)
app.register_blueprint(device_settings_bp)
app.register_blueprint(user)
app.register_blueprint(dashboard_bp)
app.register_blueprint(api_doc, url_prefix='/api')
app.register_blueprint(sdk_generator)
app.register_blueprint(departments_api)
app.register_blueprint(shifts_bp)
app.register_blueprint(employee_shifts_bp)
app.register_blueprint(attendance_api)
app.register_blueprint(leave_types_api)
app.register_blueprint(leave_policies_api)
app.register_blueprint(leave_balances_api)
app.register_blueprint(leave_requests_api)
app.register_blueprint(leave_balance_doctor_api)
app.register_blueprint(leave_audit_api)
app.register_blueprint(leave_analytics_api)
app.register_blueprint(approval_api)
app.register_blueprint(payroll_policies_api)
app.register_blueprint(dynamic_payroll_api)  # Register first for more specific routes
app.register_blueprint(employee_salary_api)
app.register_blueprint(payroll_processing_api)
app.register_blueprint(payroll_settings_api)
app.register_blueprint(countries_api)
app.register_blueprint(policy_types_api)
app.register_blueprint(tax_brackets_api)
app.register_blueprint(deduction_types_api)
app.register_blueprint(country_setup_api)

# Register documented endpoints with the API
with app.app_context():
    from application.api_docs import register_documented_endpoints
    register_documented_endpoints()

# import models so that tables will be created when you run flask
from application.Models.company import Company, CompanyDevice
from application.Models.country import Country
from application.Models.user import User
from application.Models.refreshtoken import RefreshToken
from application.Models.employees import Employee, Department, Attendance, Shift, EmployeeShift, LeaveType, LeavePolicy, LeaveBalance, LeaveRequest, Announcement, AnnouncementRead
from application.Models.approval import ApprovableEntity, ApprovalLevel, ApprovalFlow, ApprovalWorkflow, ApprovalRecord, ApprovalAssignment


# Payroll-related central models
from application.Models.employee_type import EmployeeType  # noqa: F401
from application.Models.payroll_policy_type import PayrollPolicyType  # noqa: F401
from application.Models.payroll_policy import PayrollPolicy  # noqa: F401
from application.Models.tax_bracket import TaxBracket  # noqa: F401
from application.Models.deduction_type import DeductionType  # noqa: F401
from application.Models.deduction_policy import DeductionPolicy  # noqa: F401
from application.Models.calculation_rule import CalculationRule  # noqa: F401

# Create all tables if they don't exist
with app.app_context():
    central_db.create_all()

if __name__ == '__main__':
    print("start")

    try:
        app.run(debug=True, host='0.0.0.0', port=9000)
    finally:
        pass
        # Stop the thread when the app is shut down


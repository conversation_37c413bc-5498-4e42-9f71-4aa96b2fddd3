"""added tables for announcement management

Revision ID: 5de622df3db3
Revises: 433729d33b4c
Create Date: 2025-06-16 21:00:34.896058

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5de622df3db3'
down_revision = '433729d33b4c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('announcements',
    sa.Column('announcement_id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('summary', sa.String(length=500), nullable=True),
    sa.Column('announcement_type', sa.String(length=50), nullable=False),
    sa.Column('priority', sa.String(length=20), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('target_audience', sa.String(length=50), nullable=False),
    sa.Column('department_ids', sa.Text(), nullable=True),
    sa.Column('role_targets', sa.Text(), nullable=True),
    sa.Column('employee_ids', sa.Text(), nullable=True),
    sa.Column('is_published', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('publish_date', sa.DateTime(), nullable=True),
    sa.Column('expiry_date', sa.DateTime(), nullable=True),
    sa.Column('scheduled_publish', sa.Boolean(), nullable=False),
    sa.Column('is_pinned', sa.Boolean(), nullable=False),
    sa.Column('allows_comments', sa.Boolean(), nullable=False),
    sa.Column('requires_acknowledgment', sa.Boolean(), nullable=False),
    sa.Column('attachment_urls', sa.Text(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=False),
    sa.Column('read_count', sa.Integer(), nullable=False),
    sa.Column('acknowledgment_count', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('updated_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('announcement_id')
    )
    op.create_table('announcement_reads',
    sa.Column('read_id', sa.UUID(), nullable=False),
    sa.Column('announcement_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('read_at', sa.DateTime(), nullable=False),
    sa.Column('is_acknowledged', sa.Boolean(), nullable=False),
    sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
    sa.Column('time_spent_reading', sa.Integer(), nullable=True),
    sa.Column('device_type', sa.String(length=50), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['announcement_id'], ['announcements.announcement_id'], ),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('read_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('announcement_reads')
    op.drop_table('announcements')
    # ### end Alembic commands ###

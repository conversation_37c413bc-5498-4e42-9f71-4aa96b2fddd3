"""added the employee_id in the employees table and employeesalary table

Revision ID: 433729d33b4c
Revises: c3e5e45311c4
Create Date: 2025-06-14 16:19:30.222186

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '433729d33b4c'
down_revision = 'c3e5e45311c4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employee_salaries', 'employee_type_id')
    op.add_column('employees', sa.Column('employee_type_id', sa.UUID(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('employees', 'employee_type_id')
    op.add_column('employee_salaries', sa.Column('employee_type_id', sa.UUID(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###

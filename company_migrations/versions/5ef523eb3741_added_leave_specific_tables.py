"""added leave specific tables

Revision ID: 5ef523eb3741
Revises: 019d5c4f0bc4
Create Date: 2025-05-24 20:40:27.421279

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5ef523eb3741'
down_revision = '019d5c4f0bc4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('approvable_entities',
    sa.Column('entity_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('entity_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('approval_levels',
    sa.Column('level_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('level_id')
    )
    op.create_table('leave_types',
    sa.Column('leave_type_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_paid', sa.Boolean(), nullable=False),
    sa.Column('requires_approval', sa.Boolean(), nullable=False),
    sa.Column('requires_documentation', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('leave_type_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('approval_assignments',
    sa.Column('assignment_id', sa.UUID(), nullable=False),
    sa.Column('level_id', sa.UUID(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['entity_id'], ['approvable_entities.entity_id'], ),
    sa.ForeignKeyConstraint(['level_id'], ['approval_levels.level_id'], ),
    sa.PrimaryKeyConstraint('assignment_id')
    )
    op.create_table('approval_flows',
    sa.Column('flow_id', sa.UUID(), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=False),
    sa.Column('level_id', sa.UUID(), nullable=False),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['entity_id'], ['approvable_entities.entity_id'], ),
    sa.ForeignKeyConstraint(['level_id'], ['approval_levels.level_id'], ),
    sa.PrimaryKeyConstraint('flow_id')
    )
    op.create_table('leave_policies',
    sa.Column('policy_id', sa.UUID(), nullable=False),
    sa.Column('leave_type_id', sa.UUID(), nullable=False),
    sa.Column('country_id', sa.UUID(), nullable=False),
    sa.Column('days_allowed', sa.Float(), nullable=False),
    sa.Column('accrual_period', sa.String(length=50), nullable=True),
    sa.Column('accrual_rate', sa.Float(), nullable=True),
    sa.Column('max_carryover', sa.Float(), nullable=True),
    sa.Column('min_service_days', sa.Integer(), nullable=False),
    sa.Column('is_prorated', sa.Boolean(), nullable=False),
    sa.Column('gender_specific', sa.String(length=10), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['leave_type_id'], ['leave_types.leave_type_id'], ),
    sa.PrimaryKeyConstraint('policy_id')
    )
    op.create_table('approval_workflows',
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=False),
    sa.Column('current_flow_id', sa.UUID(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['current_flow_id'], ['approval_flows.flow_id'], ),
    sa.PrimaryKeyConstraint('workflow_id')
    )
    op.create_table('leave_balances',
    sa.Column('balance_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('leave_type_id', sa.UUID(), nullable=False),
    sa.Column('year', sa.Integer(), nullable=False),
    sa.Column('total_days', sa.Float(), nullable=False),
    sa.Column('used_days', sa.Float(), nullable=False),
    sa.Column('pending_days', sa.Float(), nullable=False),
    sa.Column('carried_over_days', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['leave_type_id'], ['leave_types.leave_type_id'], ),
    sa.PrimaryKeyConstraint('balance_id')
    )
    op.create_table('leave_requests',
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('leave_type_id', sa.UUID(), nullable=False),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=False),
    sa.Column('total_days', sa.Float(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('documentation_path', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['leave_type_id'], ['leave_types.leave_type_id'], ),
    sa.PrimaryKeyConstraint('request_id')
    )
    op.create_table('approval_records',
    sa.Column('record_id', sa.UUID(), nullable=False),
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('flow_id', sa.UUID(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('comments', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['flow_id'], ['approval_flows.flow_id'], ),
    sa.ForeignKeyConstraint(['workflow_id'], ['approval_workflows.workflow_id'], ),
    sa.PrimaryKeyConstraint('record_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('approval_records')
    op.drop_table('leave_requests')
    op.drop_table('leave_balances')
    op.drop_table('approval_workflows')
    op.drop_table('leave_policies')
    op.drop_table('approval_flows')
    op.drop_table('approval_assignments')
    op.drop_table('leave_types')
    op.drop_table('approval_levels')
    op.drop_table('approvable_entities')
    # ### end Alembic commands ###

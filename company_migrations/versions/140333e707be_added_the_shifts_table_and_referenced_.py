"""added the shifts table and referenced it from the attendance table

Revision ID: 140333e707be
Revises: 418af72ed815
Create Date: 2025-05-14 19:23:08.963959

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '140333e707be'
down_revision = '418af72ed815'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('shifts',
    sa.Column('shift_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('start_time', sa.Time(), nullable=False),
    sa.Column('end_time', sa.Time(), nullable=False),
    sa.Column('grace_period_late', sa.Integer(), nullable=False),
    sa.Column('grace_period_early', sa.Integer(), nullable=False),
    sa.Column('break_duration', sa.Integer(), nullable=False),
    sa.Column('break_start_time', sa.Time(), nullable=True),
    sa.Column('is_night_shift', sa.Boolean(), nullable=False),
    sa.Column('is_flexible', sa.Boolean(), nullable=False),
    sa.Column('working_days', sa.String(length=20), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('shift_id')
    )
    op.create_table('employee_shifts',
    sa.Column('assignment_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('shift_id', sa.UUID(), nullable=False),
    sa.Column('effective_start_date', sa.Date(), nullable=False),
    sa.Column('effective_end_date', sa.Date(), nullable=True),
    sa.Column('custom_start_time', sa.Time(), nullable=True),
    sa.Column('custom_end_time', sa.Time(), nullable=True),
    sa.Column('custom_break_duration', sa.Integer(), nullable=True),
    sa.Column('custom_working_days', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['shift_id'], ['shifts.shift_id'], ),
    sa.PrimaryKeyConstraint('assignment_id')
    )
    op.add_column('attendance', sa.Column('shift_id', sa.UUID(), nullable=True))
    op.add_column('attendance', sa.Column('expected_start_time', sa.Time(), nullable=True))
    op.add_column('attendance', sa.Column('expected_end_time', sa.Time(), nullable=True))
    op.add_column('attendance', sa.Column('is_overtime', sa.Boolean(), nullable=True))
    op.add_column('attendance', sa.Column('overtime_hours', sa.Float(), nullable=True))
    op.create_foreign_key(None, 'attendance', 'shifts', ['shift_id'], ['shift_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'attendance', type_='foreignkey')
    op.drop_column('attendance', 'overtime_hours')
    op.drop_column('attendance', 'is_overtime')
    op.drop_column('attendance', 'expected_end_time')
    op.drop_column('attendance', 'expected_start_time')
    op.drop_column('attendance', 'shift_id')
    op.drop_table('employee_shifts')
    op.drop_table('shifts')
    # ### end Alembic commands ###

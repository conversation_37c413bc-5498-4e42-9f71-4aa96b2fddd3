import threading
import time
from datetime import datetime
from flask import app
from application.Models.MachineCommand import Machine<PERSON>ommand,find_pending_command,update_command_status,update_machine_command,update_machine_command_o
from application.Models.Device import Device,get_device_by_serial_num
from application.web_socket.WebSocketPool import WebSocket<PERSON>ool,ws_device
from application.Helpers.device_logger import device_logger, log_device_info, log_device_error, log_device_warning, log_command_activity

class SendOrderJob(threading.Thread):
    def __init__(self):
        threading.Thread.__init__(self)
        self.stop_event = threading.Event()
        # self.machine_command_mapper = machine_command_mapper
        # self.device_mapper = device_mapper
        self.wd_list = {}  # WebSocketPool.wsDevice
        self._thread=None
    

    def run_job(self):
        from app import app, db_connection
        from application.Models.company import CompanyDevice

        log_device_info("SendOrder<PERSON>ob started")

        while not self.stop_event.is_set():
            for key, device_status in self.wd_list.items():
                # Only log when there are devices connected
                if len(self.wd_list) > 0:
                    device_logger.debug(f"SendOrderJob running with {len(self.wd_list)} devices")

                with app.app_context():
                    try:
                        # Get the right database
                        try:
                            database_name = CompanyDevice.get_database_name_by_sn(key)
                            device_logger.log_database_activity(key, database_name, "connected")
                        except Exception as e:
                            log_device_error(f"Failed to get database name", key, e)
                            continue

                        # connect to the database
                        with db_connection.get_session(database_name) as session:

                            try:
                                in_sending = find_pending_command(session, 0, key)
                            except Exception as e:
                                log_device_error(f"Failed to find pending command", key, e)
                                continue

                            # Only log when there are actual commands to process
                            if in_sending:
                                log_command_activity(key, len(in_sending), "processing")
                                try:
                                    pending_command = find_pending_command(session, 1, key)
                                except Exception as e:
                                    log_device_error(f"Failed to find pending command", key, e)
                                    continue

                                if not pending_command:
                                    if isinstance(device_status, dict):
                                        websock = device_status['websocket']
                                        try:
                                            sent = websock.send(in_sending[0].content)
                                            device_logger.log_websocket_activity(key, "command_sent", f"Content: {in_sending[0].content[:50]}...")
                                        except Exception as e:
                                            log_device_error(f"Failed to send content via websocket", key, e)
                                    else:
                                        websock = device_status.websocket
                                        try:
                                            sent = websock.send(in_sending[0].content)
                                            device_logger.log_websocket_activity(key, "command_sent", f"Content: {in_sending[0].content[:50]}...")
                                        except Exception as e:
                                            log_device_error(f"Failed to send content via websocket", key, e)

                                    now = datetime.fromtimestamp(time.time())
                                    try:
                                        updating = update_command_status(session,0, 1, now,in_sending[0].id)
                                        log_device_info(f"Command status updated", key)
                                    except Exception as e:
                                        log_device_error(f"Failed to update command status", key, e)
                                elif len(pending_command) == 1:
                                    run_time = pending_command[0].run_time
                                    now = datetime.fromtimestamp(time.time())
                                    difference = (now - run_time).total_seconds()

                                    device_logger.debug(f"Checking pending command timing - difference: {difference}s", key)

                                    if difference > 20: #20
                                        if pending_command[0].err_count < 3:
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            machine_command.run_time = now

                                            try:
                                                updated_command = update_machine_command_o(session, machine_command)
                                                log_device_info(f"Machine command updated (attempt {machine_command.err_count})", key)
                                            except Exception as e:
                                                log_device_error(f"Failed to update machine command", key, e)

                                            try:
                                                device = get_device_by_serial_num(session, pending_command[0].serial)
                                                device_logger.log_device_status(key, f"Retrieved device status: {device.status}")
                                            except Exception as e:
                                                log_device_error(f"Failed to get device by serial number", key, e)
                                                continue

                                            if device.status != 0:
                                                if isinstance(device_status,dict):
                                                    websock = device_status['websocket']
                                                    try:
                                                        websock.send(pending_command[0].content)
                                                        device_logger.log_websocket_activity(key, "retry_command_sent", f"Attempt {machine_command.err_count}")
                                                    except Exception as e:
                                                        log_device_error(f"Failed to send retry command", key, e)
                                                else:
                                                    websock = device_status.websocket
                                                    try:
                                                        websock.send(pending_command[0].content)
                                                        device_logger.log_websocket_activity(key, "retry_command_sent", f"Attempt {machine_command.err_count}")
                                                    except Exception as e:
                                                        log_device_error(f"Failed to send retry command", key, e)
                                        else:
                                            log_device_warning(f"Command failed after 3 attempts, giving up", key)
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            try:
                                                update_machine_command_o(session, machine_command)
                                            except Exception as e:
                                                log_device_error(f"Failed to update failed command", key, e)
                                    else:
                                        device_logger.debug(f"Command timing OK - waiting (difference: {difference}s)", key)
                            # Handle case where no immediate commands but pending ones exist
                            else:
                                pending_command = find_pending_command(session, 1, key)
                                if pending_command:
                                    time_diff = (datetime.now() - pending_command[0].run_time).total_seconds()
                                    if time_diff > 20:
                                        if pending_command[0].err_count < 3:
                                            now = datetime.fromtimestamp(time.time())
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            machine_command.run_time = now

                                            try:
                                                update_machine_command_o(session, machine_command)
                                                log_device_info(f"Pending command updated (attempt {machine_command.err_count})", key)
                                            except Exception as e:
                                                log_device_error(f"Failed to update pending command", key, e)
                                                continue

                                            try:
                                                device = get_device_by_serial_num(session, pending_command[0].serial)
                                            except Exception as e:
                                                log_device_error(f"Failed to get device for pending command", key, e)
                                                continue

                                            if device.status != 0:
                                                if isinstance(device_status, dict):
                                                    websock = device_status['websocket']
                                                    try:
                                                        websock.send(pending_command[0].content)
                                                        device_logger.log_websocket_activity(key, "pending_command_sent", f"Retry attempt {machine_command.err_count}")
                                                    except Exception as e:
                                                        log_device_error(f"Failed to send pending command", key, e)
                                                else:
                                                    websock = device_status.websocket
                                                    try:
                                                        websock.send(pending_command[0].content)
                                                        device_logger.log_websocket_activity(key, "pending_command_sent", f"Retry attempt {machine_command.err_count}")
                                                    except Exception as e:
                                                        log_device_error(f"Failed to send pending command", key, e)
                                        else:
                                            log_device_warning(f"Pending command failed after 3 attempts", key)
                                            machine_command = pending_command[0]
                                            machine_command.err_count += 1
                                            try:
                                                update_machine_command_o(session, machine_command)
                                            except Exception as e:
                                                log_device_error(f"Failed to update failed pending command", key, e)

                    except Exception as e:
                        log_device_error(f"Unexpected error in device processing", key, e)

            time.sleep(1)  # Avoid high CPU usage

    def stop(self):
        self.stop_event.set()

    # def start_thread(self):
    #     self.start()

    def start_thread(self):
        if self._thread is None or not self._thread.is_alive():
            print("SendOrderJob start running-start_thread")
            self.wd_list=ws_device
            self._thread = threading.Thread(target=self.run_job)
            self._thread.start()

    def is_running(self):
        return self._thread is not None and self._thread.is_alive()
    def stop_thread(self):
        self.stop()

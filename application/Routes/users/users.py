from flask import Blueprint, request, jsonify, current_app as app, render_template, redirect, url_for
import os
from application.Models.user import User
from flask import flash
from application.Models.refreshtoken import RefreshToken
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.api_doc_decorators import document_api
from application.api_docs import user_ns, login_input_model, login_response_model, register_input_model, register_response_model, error_model, user_model, api, fields

user = Blueprint('user', __name__)

@user.route('/register_user', methods=['POST'])
@document_api(
    namespace=user_ns,
    name='register_user',
    description='Register a new user via API endpoint.',
    expect=register_input_model,
    responses={
        200: ('User created', register_response_model),
        400: ('Missing required fields', error_model),
        409: ('Validation failed', error_model),
        500: ('Server error', error_model)
    },
    examples={
        'post': {
            'Valid registration': {
                'summary': 'Valid registration example',
                'value': {
                    'first_name': '<PERSON>',
                    'last_name': 'Doe',
                    'email': '<EMAIL>',
                    'confirm_email': '<EMAIL>',
                    'password': 'password123',
                    'confirm_password': 'password123',
                    'phone_number': '1234567890',
                    'role': 'employee'
                }
            }
        }
    }
)
def register_user():
    """Register a new user via API endpoint."""
    data = request.get_json()
    app.logger.info(f"Received registration data: {data}")

    first_name = data.get('first_name')
    last_name = data.get('last_name')
    email = data.get('email')
    confirm_email = data.get('confirm_email')
    password = data.get('password')
    confirm_password = data.get('confirm_password')
    phone_number = data.get('phone_number')
    role = data.get('role')

    # Validation - Check for missing fields and provide specific feedback
    missing_fields = []
    if not first_name:
        missing_fields.append("first_name")
    if not last_name:
        missing_fields.append("last_name")
    if not email:
        missing_fields.append("email")
    if not confirm_email:
        missing_fields.append("confirm_email")
    if not password:
        missing_fields.append("password")
    if not confirm_password:
        missing_fields.append("confirm_password")
    if not phone_number:
        missing_fields.append("phone_number")
    if not role:
        missing_fields.append("role")

    if missing_fields:
        return jsonify({
            "registered": False,
            "message": "Missing required fields.",
            "missing_fields": missing_fields
        }), 400

    # Validation - Check for matching fields and duplicates
    validation_errors = []

    if password != confirm_password:
        validation_errors.append({"field": "confirm_password", "error": "Passwords do not match"})

    if email != confirm_email:
        validation_errors.append({"field": "confirm_email", "error": "Emails do not match"})
    username = email

    # Check for existing data in the database
    if User.check_user_existence('username', username):
        validation_errors.append({"field": "username", "error": "Username already exists"})

    if User.check_user_existence('email', email):
        validation_errors.append({"field": "email", "error": "Email already exists"})

    if User.check_user_existence('phone_number', phone_number):
        validation_errors.append({"field": "phone_number", "error": "Phone number already exists"})

    if validation_errors:
        return jsonify({
            "registered": False,
            "message": "Validation failed",
            "errors": validation_errors
        }), 409

    try:
        success, user = User.register_user(
            username=username,
            email=email,
            password=password,
            phone_number=phone_number,
            first_name=first_name,
            last_name=last_name,
            role=role
        )
        if success:
            app.logger.info(f"User registered: {user.username}")
            # create a company and make sure the database is created

            return jsonify({
                "registered": True,
                "user": {
                    "id": user.user_id,
                    "username": user.username,
                    "name": f"{user.first_name} {user.last_name}",
                    "email": user.email,
                    "role": user.role
                }
            }), 200
        else:
            app.logger.error(f"User registration failed: {user}")
            return jsonify({
                "registered": False,
                "message": "Failed to register user",
                "error": str(user) if user else "Unknown error"
            }), 500

    except Exception as e:
        app.logger.error(f"Registration failed: {str(e)}")
        return jsonify({
            "registered": False,
            "message": "Failed to register user",
            "error": str(e)
        }), 500



@user.route('/login', methods=['POST'])
@document_api(
    namespace=user_ns,
    name='login_user',
    description='Unified login for both central users (HR/Admin) and company users (employees).',
    expect=login_input_model,
    responses={
        200: ('Success', login_response_model),
        401: ('Invalid credentials', error_model),
        404: ('User not found', error_model),
        500: ('Server error', error_model)
    },
    examples={
        'post': {
            'Valid login examples': {
                'summary': 'Login examples for different user types',
                'value': {
                    'central_user': {
                        'username': '<EMAIL>',
                        'password': 'password123'
                    },
                    'company_user': {
                        'username': 'kiarafbo.jnabdia',  # Encoded username
                        'password': 'Q2!jMGJ53n6h'
                    }
                }
            }
        }
    }
)
def login():
    """Unified login endpoint that handles both central users and company users."""
    from application.utils.username_encoder import extract_company_id_from_username
    from application.Models.employees.company_user import CompanyUser
    from application.Models.company import Company

    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({"authenticated": False, "message": "Username and password are required"}), 400

    # Step 1: Try to extract company ID from username (for company users)
    company_id, original_username = extract_company_id_from_username(username)

    if company_id:
        # This is a company user with encoded username
        app.logger.info(f"Attempting company user login for: {original_username} (company: {company_id})")

        try:
            # Get the database name for the company
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                app.logger.error(f"Company with ID {company_id} not found")
                return jsonify({"authenticated": False, "message": "Invalid credentials"}), 401

            # Connect to the company database
            from app import db_connection
            with db_connection.get_session(database_name) as session:
                # Get the company user
                company_user = CompanyUser.get_user_by_username(session, original_username)
                if not company_user:
                    app.logger.error(f"Company user not found: {original_username}")
                    return jsonify({"authenticated": False, "message": "Invalid credentials"}), 401

                # Verify password
                if not CompanyUser.verify_password(company_user, password):
                    app.logger.error(f"Invalid password for company user: {original_username}")
                    return jsonify({"authenticated": False, "message": "Invalid credentials"}), 401

                # Check if user is active
                if not company_user.is_active:
                    app.logger.error(f"Company user account is inactive: {original_username}")
                    return jsonify({"authenticated": False, "message": "Account is inactive"}), 401

                # Update last login
                from datetime import datetime
                company_user.last_login = datetime.now()
                session.commit()

                # Generate tokens with company context
                token_payload = {
                    'user_id': str(company_user.id),
                    'username': company_user.username,
                    'role': company_user.role,
                    'user_type': 'company_user',
                    'company_id': company_id,
                    'company_db': database_name,
                    'employee_id': str(company_user.employee_id) if company_user.employee_id else None
                }

                try:
                    access_token = RefreshToken.generate_custom_token(token_payload)
                    refresh_token = RefreshToken.generate_custom_refresh_token(token_payload)
                    app.logger.info(f"Tokens generated for company user: {original_username}")
                except Exception as e:
                    app.logger.error(f"Error generating tokens for company user: {str(e)}")
                    return jsonify({"authenticated": False, "message": "Error generating authentication tokens"}), 500

                # Get company information
                company = Company.get_company_by_id(company_id)
                company_info = {
                    "company_id": company_id,
                    "company_name": company.company_name if company else "Unknown",
                    "database_name": database_name
                }

                return jsonify({
                    "authenticated": True,
                    "user_type": "company_user",
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "user": {
                        "id": str(company_user.id),
                        "username": company_user.username,
                        "role": company_user.role,
                        "name": f"{company_user.first_name} {company_user.last_name}",
                        "employee_id": str(company_user.employee_id) if company_user.employee_id else None,
                        "employee_info": company_user.employee.to_dict() if company_user.employee else None
                    },
                    "company": company_info,
                    "redirect_to": "employee_portal"  # Frontend can use this for routing
                }), 200

        except Exception as e:
            app.logger.error(f"Error during company user login: {str(e)}")
            return jsonify({"authenticated": False, "message": "Login failed"}), 500

    else:
        # This is a central user (HR/Admin) - original logic
        app.logger.info(f"Attempting central user login for: {username}")

        try:
            user = User.get_user_by_username(username)
            if not user:
                app.logger.error(f"Central user not found: {username}")
                return jsonify({"authenticated": False, "message": "Invalid credentials"}), 401
        except Exception as e:
            app.logger.error(f"Error fetching central user: {str(e)}")
            return jsonify({"authenticated": False, "message": "Login failed"}), 500

        # Verify password
        result = User.login_user(username, password)
        if not result:
            app.logger.error(f"Invalid password for central user: {username}")
            return jsonify({"authenticated": False, "message": "Invalid credentials"}), 401

        try:
            # Generate tokens for central user
            access_token = RefreshToken.generate_access_token(user)
            refresh_token = RefreshToken.generate_refresh_token(user)
            app.logger.info(f"Tokens generated for central user: {user.username}")
        except Exception as e:
            app.logger.error(f"Error generating tokens for central user: {str(e)}")
            return jsonify({"authenticated": False, "message": "Error generating authentication tokens"}), 500

        # Get company information if the user is associated with any company
        companies_info = []
        try:
            if hasattr(user, 'companies') and user.companies:
                for company in user.companies:
                    company_info = {
                        "company_id": company.company_id,
                        "company_name": company.company_name,
                        "database_name": company.database_name
                    }
                    companies_info.append(company_info)
                app.logger.info(f"Company information for central user {user.username}: {companies_info}")
        except Exception as e:
            app.logger.error(f"Error getting company information: {str(e)}")

        return jsonify({
            "authenticated": True,
            "user_type": "central_user",
            "access_token": access_token,
            "refresh_token": refresh_token,
            "user": {
                "id": user.user_id,
                "username": user.username,
                "role": user.role,
                "name": f"{user.first_name} {user.last_name}"
            },
            "companies": companies_info,
            "redirect_to": "admin_portal"  # Frontend can use this for routing
        }), 200

@user.route('/logout', methods=['POST'])
@document_api(
    namespace=user_ns,
    name='logout_user',
    description='Logout a user by revoking their refresh token.',
    security='Bearer Auth',
    responses={
        200: ('Logout successful', None),
        400: ('Logout failed', error_model)
    }
)
def logout():
    """Revoke a refresh token."""
    token = request.headers.get('Authorization').split(" ")[1]
    if RefreshToken.revoke_token(token):
        return jsonify({"message": "Logout successful"}), 200
    else:
        return jsonify({"message": "Logout failed"}), 400


@user.route('/get_user/<user_id>', methods=['GET'])
@token_required
@document_api(
    namespace=user_ns,
    name='get_user',
    description='Get a user by their ID. Returns detailed user information including associated companies.',
    security='Bearer Auth',
    params=[
        {'name': 'user_id', 'description': 'The user identifier', '_in': 'path', 'type': 'string'}
    ],
    responses={
        200: ('Success', api.model('GetUserResponse', {
            'success': fields.Boolean(required=True, description='Success status'),
            'user': fields.Nested(user_model)
        })),
        404: ('User not found', error_model),
        500: ('Server error', error_model)
    }
)
def get_user_by_id(user_id):
    """
    Get a user by their ID.

    This endpoint returns detailed user information including associated companies.
    """
    try:
        # Get the user by ID
        user_data = User.get_user_by_id(user_id)

        if not user_data:
            app.logger.error(f"User with ID {user_id} not found")
            return jsonify({"success": False, "message": "User not found"}), 404

        app.logger.info(f"Retrieved user data for ID {user_id}")
        return jsonify({
            "success": True,
            "user": user_data
        }), 200

    except Exception as e:
        app.logger.error(f"Error retrieving user with ID {user_id}: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Failed to retrieve user information",
            "error": str(e)
        }), 500



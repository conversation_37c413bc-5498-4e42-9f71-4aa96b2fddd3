from flask import Blueprint, request, jsonify
import uuid
from application.Models.company import Company, CompanyDevice
from application.Models.country import Country
from application.automations.create_dynamic_db import create_database
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Services.rwanda_leave_setup_service import RwandaLeaveSetupService
from application.config.leave_setup_config import LeaveSetupConfig

company_bp = Blueprint('company', __name__)

@company_bp.route('/add_company', methods=['POST'])
@token_required
@roles_required('hr')
def add_company():
    """Add a new company."""
    from app import app
    from application.Models.user import User
    from flask import g

    data = request.get_json()
    company_name = data.get('company_name')
    database_name = data.get('database_name')
    company_id = data.get('company_id')
    company_tin = data.get('company_tin')
    phone_number = data.get('phone_number')


    # check if company_tin is not provided and prompt a user to provide it
    if not company_tin:
        return jsonify({"message": "Company TIN is required"}), 400

    #check if the company_tin is already registered
    if Company.get_company_by_tin(company_tin):
        return jsonify({"message": "Company TIN already exists"}), 400

    # Check if company id is not provided
    if not company_id:
        # generate a company id as a uuid
        company_id = str(uuid.uuid4())

    # Check if database name is not provided
    if not database_name:
        # Generate a database name from the company_tin plus a random part followed bd _db
        random_part = str(uuid.uuid4()).split('-')[0]

        database_name = f"{company_tin}_{random_part}_db"

    # add the company to the database
    try:
        result = Company.create_company(company_id=company_id, company_name=company_name, database_name=database_name, company_tin=company_tin, phone_number=phone_number)
        message = f"Company '{company_name}' added successfully."

        # Create a new PostgreSQL database and tables for the new company
        create_database(database_name)
        app.logger.info(f"Database '{database_name}' created successfully.")

        # Auto-setup leave policies based on country
        leave_setup_result = None
        try:
            # Check if automatic leave setup is enabled
            if LeaveSetupConfig.is_auto_setup_enabled():
                # Get company's country
                company_country = None
                if hasattr(result, 'country_id') and result.country_id:
                    company_country = Country.get_country_by_id(result.country_id)

                country_code = company_country.code if company_country else LeaveSetupConfig.DEFAULT_COUNTRY_SETUP

                # Check if setup should be performed for this country
                if LeaveSetupConfig.should_setup_for_country(country_code):
                    setup_config = LeaveSetupConfig.get_country_setup_config(country_code)

                    app.logger.info(f"Setting up {setup_config['description']} for company {company_id}")

                    # Currently only Rwanda is supported, but this can be extended
                    if country_code == 'RW':
                        leave_setup_result = RwandaLeaveSetupService.setup_rwanda_leave_policies(company_id)

                    if leave_setup_result and leave_setup_result.get('success'):
                        app.logger.info(f"Leave policies setup successful: {leave_setup_result.get('message')}")
                        message += f" {setup_config['description']} configured automatically."
                    else:
                        app.logger.warning(f"Leave policies setup failed: {leave_setup_result.get('message') if leave_setup_result else 'Unknown error'}")
                        message += f" Warning: Automatic leave setup failed."
                else:
                    app.logger.info(f"Company {company_id} country '{country_code}' does not have automatic leave setup configured")
            else:
                app.logger.info(f"Automatic leave setup is disabled")

        except Exception as e:
            app.logger.error(f"Error during automatic leave setup for company {company_id}: {str(e)}")
            message += f" Warning: Automatic leave setup failed."

        # Get the current user's ID from the token
        current_user_id = g.user.get('user_id')

        if current_user_id:
            # Assign the current user (HR) to the newly created company
            assignment_result = User.assign_user_a_company(current_user_id, company_id)

            if assignment_result.get('success'):
                app.logger.info(f"User {current_user_id} assigned to company {company_id} successfully.")
                response_data = {
                    "message": message,
                    "company": result.to_dict(),
                    "user_assigned": True,
                    "assignment_details": assignment_result.get('message')
                }

                # Add leave setup details if available
                if leave_setup_result:
                    response_data["leave_setup"] = leave_setup_result

                return jsonify(response_data), 200
            else:
                app.logger.error(f"Failed to assign user {current_user_id} to company {company_id}: {assignment_result.get('message')}")
                response_data = {
                    "message": message,
                    "company": result.to_dict(),
                    "user_assigned": False,
                    "assignment_error": assignment_result.get('message'),
                    "error_code": assignment_result.get('error_code')
                }

                # Add leave setup details if available
                if leave_setup_result:
                    response_data["leave_setup"] = leave_setup_result

                return jsonify(response_data), 200
        else:
            app.logger.warning("User ID not found in token. Cannot assign user to company.")
            response_data = {
                "message": message,
                "company": result.to_dict(),
                "user_assigned": False,
                "assignment_error": "User ID not found in token",
                "error_code": "USER_ID_MISSING"
            }

            # Add leave setup details if available
            if leave_setup_result:
                response_data["leave_setup"] = leave_setup_result

            return jsonify(response_data), 200

    except Exception as e:
        app.logger.error(f"An error occurred while adding the company: {str(e)}")
        return jsonify({"message": f"An error occurred while adding the company: {str(e)}"}), 500

@company_bp.route('/get_companies', methods=['GET'])
@token_required
def get_companies():
    """Return all companies."""
    companies = Company.get_companies()
    return jsonify({"companies": [company.to_dict() for company in companies]}), 200

@company_bp.route('/add_company_device', methods=['POST'])
@token_required
@roles_required('admin')
def add_company_device():
    """Add a new company device."""
    from app import app, db_connection, personService
    data = request.get_json()
    company_id = data.get('company_id')
    device_sn = data.get('device_sn')

    # add the company device to the database
    try:
        result = CompanyDevice.create_company_device(company_id=company_id, device_sn=device_sn)
        if result:
            if result == "Device already Registered with another company":
                return jsonify({"message": result}), 400

            # Get the database name for the company
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                app.logger.error(f"Could not find database name for company ID {company_id}")
                message = f"Device '{device_sn}' added successfully, but failed to send employees to device."
                return jsonify({"message": message, "device": result.to_dict()}), 200

            # Connect to the company's database
            try:
                with db_connection.get_session(database_name) as session:
                    # First, add the device to the company's device table
                    from application.Models.Device import insert_device, get_device_by_serial_num

                    # Check if device already exists in the device table
                    existing_device = get_device_by_serial_num(session, device_sn)

                    if not existing_device:
                        # Add the device to the device table with status 1 (active)
                        device_result = insert_device(session, device_sn, 1)
                        if device_result:
                            app.logger.info(f"Added device {device_sn} to company's device table")
                        else:
                            app.logger.error(f"Failed to add device {device_sn} to company's device table")
                    else:
                        app.logger.info(f"Device {device_sn} already exists in company's device table")

                    # Get all persons from the database
                    from application.Models.Person import Person
                    from application.Models.EnrollInfo import EnrollInfo
                    persons = Person.get_persons(session)

                    # If Person table is empty, try to populate it from Employee table
                    if not persons:
                        app.logger.info(f"No persons found in company {company_id}. Checking for employees...")

                        # Import Employee model
                        from application.Models.employees.employee import Employee

                        # Get all active employees
                        employees = Employee.get_all_employees(session)

                        if employees:
                            app.logger.info(f"Found {len(employees)} employees. Creating Person records...")

                            # Create Person records for each employee
                            created_persons = []
                            for employee in employees:
                                try:
                                    # Create full name from first and last name
                                    full_name = f"{employee.first_name} {employee.last_name}"

                                    # Create a new Person record
                                    person_data = {
                                        "name": full_name,
                                        "roll_id": 0,  # Default role
                                        "employee_id": str(employee.employee_id)
                                    }

                                    # Add the person to the database
                                    new_person = Person.add_employee(session, **person_data)

                                    if new_person:
                                        app.logger.info(f"Created Person record for employee: {full_name}")
                                        created_persons.append(new_person)

                                        # Create a basic EnrollInfo record with backupnum = -1
                                        enroll_info_data = {
                                            "enroll_id": new_person.id,
                                            "backupnum": -1,
                                            "imagepath": "",
                                            "signatures": ""
                                        }

                                        # Insert the EnrollInfo record
                                        from application.Models.EnrollInfo import insert_enroll_info
                                        insert_enroll_info(session, **enroll_info_data)
                                    else:
                                        app.logger.error(f"Failed to create Person record for employee: {full_name}")
                                except Exception as e:
                                    app.logger.error(f"Error creating Person record for employee {employee.employee_id}: {str(e)}")

                            # Update persons list with newly created records
                            if created_persons:
                                persons = created_persons
                                app.logger.info(f"Created {len(created_persons)} Person records from Employee data")
                            else:
                                app.logger.warning("No Person records could be created from Employee data")
                        else:
                            app.logger.info(f"No employees found in company {company_id}")

                    # Now send all persons to the device
                    if not persons:
                        app.logger.info(f"No persons found or created for company {company_id} to send to device {device_sn}")
                        message = f"Device '{device_sn}' added successfully, but no employees found to send to device."
                        return jsonify({"message": message, "device": result.to_dict()}), 200
                    else:
                        app.logger.info(f"Found {len(persons)} persons to send to device {device_sn}")

                        # Send each person to the device
                        sent_count = 0
                        for person in persons:
                            try:
                                # Default backupnum to -1 (just send the name)
                                backupnum = -1

                                # Try to get enrollment info if available
                                enroll_info = EnrollInfo.selectbybackupnum(session, person.id, backupnum)

                                # Set signatures if enrollment info exists
                                signatures = enroll_info.signatures if enroll_info else ""

                                # Send the person to the device
                                app.logger.info(f"Sending person {person.id} ({person.name}) to device {device_sn}")
                                personService.set_user_to_device(
                                    session,
                                    person.id,
                                    person.name,
                                    backupnum,
                                    person.roll_id,
                                    signatures,
                                    device_sn
                                )
                                sent_count += 1
                            except Exception as e:
                                app.logger.error(f"Error sending person {person.id} to device {device_sn}: {str(e)}")

                        app.logger.info(f"Successfully sent {sent_count} out of {len(persons)} persons to device {device_sn}")
                        message = f"Device '{device_sn}' added successfully. Sent {sent_count} employees to the device."
                        return jsonify({"message": message, "device": result.to_dict()}), 200
            except Exception as e:
                app.logger.error(f"Error connecting to database {database_name}: {str(e)}")
                message = f"Device '{device_sn}' added successfully, but failed to send employees to device: {str(e)}"
                return jsonify({"message": message, "device": result.to_dict()}), 200

            message = f"Device '{device_sn}' added successfully."
            return jsonify({"message": message, "device": result.to_dict()}), 200
        else:
            return jsonify({"message": "An error occurred while adding the device"}), 400
    except Exception as e:
        app.logger.error(f"An error occurred while adding the device: {str(e)}")
        return jsonify({"message": f"An error occurred while adding the device: {str(e)}"}), 500

@company_bp.route('/get_company_devices', methods=['GET'])
@token_required
def get_company_devices():
    """Return all company devices."""
    from app import app
    company_id = request.args.get('company_id')
    try:
        devices = CompanyDevice.get_company_devices(company_id)
        app.logger.info(f"Devices: {devices}")
    except Exception as e:
        devices = []
        app.logger.error(f"An error occurred while getting the devices: {str(e)}")
    # convert the dictionary to a list of dictionaries
    devices = [device.to_dict() for device in devices]
    # get the devices serial numbers
    devices = [device['device_sn'] for device in devices]
    return jsonify({"devices": devices}), 200

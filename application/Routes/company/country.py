"""Enhanced Country Management API for Central Database Administration."""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.country import Country
from application.Models.payroll_policy import PayrollPolicy
from application.Models.payroll_policy_type import PayrollPolicyType
from application.Models.employee_type import EmployeeType
from application.Models.deduction_type import DeductionType
from application.Models.deduction_policy import DeductionPolicy
from application.Models.tax_bracket import TaxBracket
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import central_db as db
from datetime import datetime, date
import uuid

countries_api = Blueprint('countries_api', __name__)

# ============================================================================
# COUNTRY MANAGEMENT ENDPOINTS
# ============================================================================

@countries_api.route('/api/countries', methods=['GET'])
@token_required
@roles_required('admin', 'country_admin', 'hr')
def get_all_countries():
    """Get all countries with payroll readiness status."""
    try:
        countries = Country.get_all_countries()

        countries_data = []
        for country in countries:
            country_dict = country.to_dict()

            # Add payroll readiness status
            policy_count = PayrollPolicy.query.filter_by(country_id=country.country_id).count()
            employee_types_count = EmployeeType.query.filter_by(country_id=country.country_id).count()
            deduction_types_count = DeductionType.query.filter_by(country_id=country.country_id).count()

            country_dict['payroll_status'] = {
                'is_ready': policy_count > 0 and employee_types_count > 0,
                'policies_count': policy_count,
                'employee_types_count': employee_types_count,
                'deduction_types_count': deduction_types_count,
                'setup_completion': min(100, (policy_count + employee_types_count + deduction_types_count) * 10)
            }

            countries_data.append(country_dict)

        return jsonify({
            "success": True,
            "countries": countries_data,
            "total_count": len(countries_data)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting countries: {e}")
        return jsonify({"error": "Failed to get countries"}), 500

@countries_api.route('/api/countries', methods=['POST'])
@token_required
@roles_required('admin', 'country_admin')
def create_country():
    """Create a new country."""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['name', 'code', 'currency']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400

        # Validate country code format (ISO 3166-1 alpha-2)
        country_code = data.get('code').upper()
        if len(country_code) != 2:
            return jsonify({"error": "Country code must be 2 characters (ISO 3166-1 alpha-2)"}), 400

        # Check if country already exists
        existing_country = Country.get_country_by_code(country_code)
        if existing_country:
            return jsonify({"error": f"Country with code '{country_code}' already exists"}), 409

        # Create country
        country_data = {
            'name': data.get('name'),
            'code': country_code,
            'currency': data.get('currency').upper(),
            'time_zone': data.get('time_zone'),
            'date_format': data.get('date_format', 'YYYY-MM-DD')
        }

        country = Country.create_country(**country_data)
        if not country:
            return jsonify({"error": "Failed to create country"}), 500

        app.logger.info(f"Country created: {country.name} ({country.code}) by user {g.user.get('user_id')}")

        return jsonify({
            "success": True,
            "message": f"Country '{country.name}' created successfully",
            "country": country.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating country: {e}")
        return jsonify({"error": "Failed to create country"}), 500

@countries_api.route('/api/countries/<country_id>', methods=['GET'])
@token_required
@roles_required('admin', 'country_admin', 'hr')
def get_country_by_id(country_id):
    """Get a country by its ID with detailed payroll configuration."""
    try:
        country = Country.get_country_by_id(country_id)
        if not country:
            return jsonify({"error": "Country not found"}), 404

        country_dict = country.to_dict()

        # Add detailed payroll configuration
        policies = PayrollPolicy.get_policies_for_country(country.country_id)
        employee_types = EmployeeType.get_by_country(country.country_id)
        deduction_types = DeductionType.get_by_country(country.country_id)

        country_dict['payroll_configuration'] = {
            'policies': [p.to_dict() for p in policies],
            'employee_types': [et.to_dict() for et in employee_types],
            'deduction_types': [dt.to_dict() for dt in deduction_types],
            'summary': {
                'total_policies': len(policies),
                'active_policies': len([p for p in policies if p.is_active]),
                'total_employee_types': len(employee_types),
                'total_deduction_types': len(deduction_types)
            }
        }

        return jsonify({
            "success": True,
            "country": country_dict
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country: {e}")
        return jsonify({"error": "Failed to get country"}), 500

@countries_api.route('/api/countries/<country_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'country_admin')
def update_country(country_id):
    """Update a country's details."""
    try:
        country = Country.get_country_by_id(country_id)
        if not country:
            return jsonify({"error": "Country not found"}), 404

        data = request.get_json()

        # Update allowed fields
        allowed_fields = ['name', 'currency', 'time_zone', 'date_format']
        for field in allowed_fields:
            if field in data:
                setattr(country, field, data[field])

        country.updated_at = db.func.now()
        db.session.commit()

        app.logger.info(f"Country updated: {country.name} ({country.code}) by user {g.user.get('user_id')}")

        return jsonify({
            "success": True,
            "message": f"Country '{country.name}' updated successfully",
            "country": country.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error updating country: {e}")
        return jsonify({"error": "Failed to update country"}), 500

@countries_api.route('/api/countries/<country_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_country(country_id):
    """Delete a country (admin only)."""
    try:
        country = Country.get_country_by_id(country_id)
        if not country:
            return jsonify({"error": "Country not found"}), 404

        # Check if country has associated data
        policies_count = PayrollPolicy.query.filter_by(country_id=country.country_id).count()
        employee_types_count = EmployeeType.query.filter_by(country_id=country.country_id).count()

        if policies_count > 0 or employee_types_count > 0:
            return jsonify({
                "error": "Cannot delete country with existing payroll configurations",
                "details": {
                    "policies": policies_count,
                    "employee_types": employee_types_count
                }
            }), 409

        country_name = country.name
        country_code = country.code

        db.session.delete(country)
        db.session.commit()

        app.logger.warning(f"Country deleted: {country_name} ({country_code}) by user {g.user.get('user_id')}")

        return jsonify({
            "success": True,
            "message": f"Country '{country_name}' deleted successfully"
        }), 200

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting country: {e}")
        return jsonify({"error": "Failed to delete country"}), 500

@countries_api.route('/api/countries/<country_id>/payroll-summary', methods=['GET'])
@token_required
@roles_required('admin', 'country_admin', 'hr')
def get_country_payroll_summary(country_id):
    """Get comprehensive payroll readiness summary for a country."""
    try:
        country = Country.get_country_by_id(country_id)
        if not country:
            return jsonify({"error": "Country not found"}), 404

        # Get all payroll-related data
        policies = PayrollPolicy.get_policies_for_country(country.country_id)
        employee_types = EmployeeType.get_by_country(country.country_id)
        deduction_types = DeductionType.get_by_country(country.country_id)
        deduction_policies = DeductionPolicy.query.filter_by(country_id=country.country_id).all()

        # Analyze policy types coverage
        policy_types_with_policies = set()
        tax_policies_count = 0

        for policy in policies:
            if policy.policy_type:
                policy_types_with_policies.add(policy.policy_type.code)
                if policy.policy_type.calculation_method == 'PROGRESSIVE_TAX':
                    tax_policies_count += 1

        # Get all available policy types
        all_policy_types = PayrollPolicyType.get_all_active()
        missing_policy_types = [pt.code for pt in all_policy_types if pt.code not in policy_types_with_policies]

        # Calculate readiness score
        readiness_factors = {
            'has_employee_types': len(employee_types) > 0,
            'has_tax_policies': tax_policies_count > 0,
            'has_deduction_types': len(deduction_types) > 0,
            'has_deduction_policies': len(deduction_policies) > 0,
            'has_default_employee_type': any(et.is_default for et in employee_types)
        }

        readiness_score = sum(readiness_factors.values()) / len(readiness_factors) * 100

        # Determine readiness status
        if readiness_score >= 80:
            readiness_status = "ready"
        elif readiness_score >= 60:
            readiness_status = "mostly_ready"
        elif readiness_score >= 40:
            readiness_status = "partially_ready"
        else:
            readiness_status = "not_ready"

        # Generate recommendations
        recommendations = []
        if not readiness_factors['has_employee_types']:
            recommendations.append("Create employee types (e.g., Permanent, Contract, Casual)")
        if not readiness_factors['has_tax_policies']:
            recommendations.append("Set up PAYE tax policies with tax brackets")
        if not readiness_factors['has_deduction_types']:
            recommendations.append("Define deduction types (e.g., Pension, Health Insurance)")
        if not readiness_factors['has_deduction_policies']:
            recommendations.append("Create deduction policies with rates")
        if not readiness_factors['has_default_employee_type']:
            recommendations.append("Set a default employee type")

        return jsonify({
            "success": True,
            "country": {
                "country_id": str(country.country_id),
                "name": country.name,
                "code": country.code,
                "currency": country.currency
            },
            "payroll_readiness": {
                "status": readiness_status,
                "score": round(readiness_score, 1),
                "factors": readiness_factors
            },
            "configuration_summary": {
                "policies": {
                    "total": len(policies),
                    "active": len([p for p in policies if p.is_active]),
                    "tax_policies": tax_policies_count,
                    "covered_types": list(policy_types_with_policies),
                    "missing_types": missing_policy_types
                },
                "employee_types": {
                    "total": len(employee_types),
                    "default_set": any(et.is_default for et in employee_types),
                    "types": [{"name": et.name, "code": et.code, "is_default": et.is_default} for et in employee_types]
                },
                "deduction_types": {
                    "total": len(deduction_types),
                    "mandatory": len([dt for dt in deduction_types if dt.is_mandatory]),
                    "types": [{"name": dt.name, "code": dt.code, "is_mandatory": dt.is_mandatory} for dt in deduction_types]
                },
                "deduction_policies": {
                    "total": len(deduction_policies),
                    "active": len([dp for dp in deduction_policies if dp.is_active])
                }
            },
            "recommendations": recommendations,
            "next_steps": [
                "Review and test payroll calculations",
                "Set up company-specific allowances",
                "Configure payroll processing schedules",
                "Train HR staff on the system"
            ] if readiness_status == "ready" else recommendations
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country payroll summary: {e}")
        return jsonify({"error": "Failed to get payroll summary"}), 500

# ============================================================================
# COUNTRY CODE LOOKUP ENDPOINTS
# ============================================================================

@countries_api.route('/api/countries/by-code/<country_code>', methods=['GET'])
@token_required
@roles_required('admin', 'country_admin', 'hr')
def get_country_by_code(country_code):
    """Get a country by its code."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        return jsonify({
            "success": True,
            "country": country.to_dict()
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country by code: {e}")
        return jsonify({"error": "Failed to get country"}), 500
from application.Models.employees import Employee, Attendance
from application.Models.employees.department import Department
from application.Models.company import Company
from datetime import datetime, timedelta, date
from sqlalchemy import func, cast, Date
import uuid
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from flask import Blueprint, request, jsonify, current_app as app
from application.Helpers.helper_methods import HelperMethods
from application.Helpers.date_helper import DateHelper

attendance_api = Blueprint('attendance_api', __name__)

# Attendance CRUD operations
@attendance_api.route('/api/attendance', methods=['GET'])
@token_required
def get_attendance_records():
    """Get attendance records with optional filtering."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Get query parameters
    employee_id = request.args.get('employee_id')
    date_str = request.args.get('date')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    # Parse dates
    date_value = HelperMethods.parse_date(date_str) if date_str else None
    start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
    end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        attendance_records = []

        if employee_id and (start_date or end_date):
            # Get attendance for specific employee in date range
            attendance_records = Attendance.get_attendance_by_employee_id(session, employee_id, start_date, end_date)
        elif employee_id:
            # Get all attendance for specific employee
            attendance_records = Attendance.get_attendance_by_employee_id(session, employee_id)
        elif date_value:
            # Get all attendance for specific date
            attendance_records = Attendance.get_attendance_by_date(session, date_value)
        else:
            # Get all attendance records (could be limited or paginated in a real app)
            attendance_records = session.query(Attendance).order_by(Attendance.date.desc()).limit(100).all()

        attendance_data = [record.to_dict() for record in attendance_records]

        return jsonify({
            "status": "success",
            "attendance_records": attendance_data
        }), 200

@attendance_api.route('/api/attendance/<attendance_id>', methods=['GET'])
@token_required
def get_attendance(attendance_id):
    """Get a specific attendance record by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        attendance = Attendance.get_attendance_by_id(session, attendance_id)

        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        return jsonify({
            "status": "success",
            "attendance": attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/daily', methods=['GET'])
@token_required
def get_daily_attendance():
    """
    Get daily attendance records with optional date filtering and department-based statistics.

    Query Parameters:
    - company_id: Required. The ID of the company to get attendance for.
    - date: Optional. The date to get attendance for in YYYY-MM-DD format. Defaults to today.
    - page: Optional. Page number for pagination. Defaults to 1.
    - per_page: Optional. Number of items per page. Defaults to 50.

    Returns:
    - JSON object with attendance statistics, department-based summaries, and employee details grouped by status.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        date_str = request.args.get('date')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))

        # Parse date (default to today if not provided)
        target_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not target_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get all active employees
            all_employees = session.query(Employee).filter(Employee.status == 'active').all()
            total_employees = len(all_employees)

            # Get all departments
            all_departments = session.query(Department).all()

            # Get attendance records for the target date
            attendance_records = Attendance.get_attendance_by_date(session, target_date)
            attendance_data = [record.to_dict() for record in attendance_records]

            # Create a mapping of employee_id to attendance record
            attendance_map = {record['employee_id']: record for record in attendance_data}

            # Initialize counters and employee lists
            present_count = 0
            absent_count = 0
            on_leave_count = 0
            present_employees = []
            absent_employees = []
            on_leave_employees = []

            # Initialize department statistics
            department_stats = {}
            # Add a special category for employees with no department
            department_stats['no_department'] = {
                'department_id': 'no_department',
                'department_name': 'No Department',
                'total_employees': 0,
                'present_count': 0,
                'absent_count': 0,
                'on_leave_count': 0,
                'attendance_percentage': 0
            }

            # Add all existing departments
            for dept in all_departments:
                department_stats[str(dept.department_id)] = {
                    'department_id': str(dept.department_id),
                    'department_name': dept.name,
                    'total_employees': 0,
                    'present_count': 0,
                    'absent_count': 0,
                    'on_leave_count': 0,
                    'attendance_percentage': 0
                }

            # Process each employee to determine their attendance status
            for employee in all_employees:
                employee_dict = employee.to_dict()
                employee_id_str = str(employee.employee_id)

                # Add department info if available
                department_id_str = None
                if employee.department_id and employee.department:
                    department_id_str = str(employee.department_id)
                    employee_dict['department_name'] = employee.department.name

                    # Update department total employee count
                    if department_id_str in department_stats:
                        department_stats[department_id_str]['total_employees'] += 1
                else:
                    # Employee has no department, add to "No Department" category
                    department_id_str = 'no_department'
                    employee_dict['department_name'] = 'No Department'
                    department_stats['no_department']['total_employees'] += 1

                # Check if employee has an attendance record for the day
                if employee_id_str in attendance_map:
                    record = attendance_map[employee_id_str]
                    status = record['status']

                    # Add attendance details to employee dict
                    employee_dict['attendance'] = {
                        'attendance_id': record['attendance_id'],
                        'check_in_time': record['check_in_time'],
                        'check_out_time': record['check_out_time'],
                        'total_hours': record['total_hours'],
                        'status': status
                    }

                    # Categorize by status
                    if status == 'present':
                        present_count += 1
                        present_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['present_count'] += 1
                    elif status == 'on leave':
                        on_leave_count += 1
                        on_leave_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['on_leave_count'] += 1
                    else:
                        # Any other status (like 'late') is still counted as present for attendance purposes
                        present_count += 1
                        present_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['present_count'] += 1
                else:
                    # No attendance record means absent
                    employee_dict['attendance'] = {
                        'status': 'absent',
                        'reason': 'No attendance record'
                    }
                    absent_count += 1
                    absent_employees.append(employee_dict)

                    # Update department stats
                    if department_id_str and department_id_str in department_stats:
                        department_stats[department_id_str]['absent_count'] += 1

            # Calculate attendance percentage for each department
            for dept_id, stats in department_stats.items():
                total = stats['total_employees']
                if total > 0:
                    stats['attendance_percentage'] = round((stats['present_count'] / total) * 100, 2)

            # Convert department stats to a list and sort by attendance percentage (descending)
            department_summary = list(department_stats.values())

            # Only include departments with employees (including "No Department" if it has employees)
            department_summary = [dept for dept in department_summary if dept['total_employees'] > 0]

            # Sort by attendance percentage (descending)
            department_summary.sort(key=lambda x: x['attendance_percentage'], reverse=True)

            # Apply pagination to each category
            def paginate_list(items, page, per_page):
                start = (page - 1) * per_page
                end = start + per_page
                return items[start:min(end, len(items))], len(items)

            present_employees_paginated, present_total = paginate_list(present_employees, page, per_page)
            absent_employees_paginated, absent_total = paginate_list(absent_employees, page, per_page)
            on_leave_employees_paginated, on_leave_total = paginate_list(on_leave_employees, page, per_page)

            return jsonify({
                "status": "success",
                "metadata": {
                    "date": target_date.strftime('%Y-%m-%d'),
                    "total_employees": total_employees
                },
                "summary": {
                    "present_count": present_count,
                    "absent_count": absent_count,
                    "on_leave_count": on_leave_count,
                    "attendance_percentage": round((present_count / total_employees) * 100, 2) if total_employees > 0 else 0
                },
                "department_summary": department_summary,
                "present_employees": {
                    "count": present_total,
                    "data": present_employees_paginated
                },
                "absent_employees": {
                    "count": absent_total,
                    "data": absent_employees_paginated
                },
                "on_leave_employees": {
                    "count": on_leave_total,
                    "data": on_leave_employees_paginated
                }
            }), 200
    except ValueError as e:
        app.logger.error(f"Value error in get_daily_attendance: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_daily_attendance: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500

@attendance_api.route('/api/attendance', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_attendance():
    """Create a new attendance record."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['employee_id', 'source']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # At least one of check_in_time or check_out_time must be provided
    if not data.get('check_in_time') and not data.get('check_out_time'):
        return jsonify({"message": "Either check-in time or check-out time is required"}), 400

    # Parse check-in time
    check_in_time = None
    if data.get('check_in_time'):
        try:
            check_in_time = datetime.strptime(data.get('check_in_time'), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"message": "Invalid check-in time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Parse check-out time
    check_out_time = None
    if data.get('check_out_time'):
        try:
            check_out_time = datetime.strptime(data.get('check_out_time'), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"message": "Invalid check-out time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Extract date from check_in_time (or check_out_time if check_in_time is not provided)
    date_value = None
    if check_in_time:
        date_value = check_in_time.date()
    elif check_out_time:
        date_value = check_out_time.date()
    else:
        # This should never happen due to the validation above, but just in case
        return jsonify({"message": "Either check-in time or check-out time is required"}), 400

    # Calculate total hours if both check-in and check-out times are provided
    total_hours = None
    if check_in_time and check_out_time:
        time_diff = check_out_time - check_in_time
        total_hours = time_diff.total_seconds() / 3600  # Convert to hours

    # Prepare attendance data
    attendance_data = {
        'employee_id': data.get('employee_id'),
        'date': date_value,
        'check_in_time': check_in_time,
        'check_out_time': check_out_time,
        'total_hours': total_hours,
        'status': data.get('status', 'present'),
        'source': data.get('source'),
        'source_record_id': data.get('source_record_id'),
        'notes': data.get('notes'),
        'created_by': data.get('created_by')
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if employee exists
        employee = Employee.get_employee_by_id(session, attendance_data['employee_id'])
        if not employee:
            return jsonify({"message": f"Employee with ID {attendance_data['employee_id']} not found"}), 404

        # Create the attendance record
        attendance = Attendance.create_attendance(session, **attendance_data)

        if not attendance:
            return jsonify({"message": "Failed to create attendance record"}), 500

        return jsonify({
            "status": "success",
            "attendance": attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/<attendance_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_attendance(attendance_id):
    """Update an existing attendance record."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Parse dates and times
    update_data = {}

    # Parse check-in time if provided
    if 'check_in_time' in data:
        try:
            check_in_time = datetime.strptime(data.get('check_in_time'), '%Y-%m-%d %H:%M:%S')
            update_data['check_in_time'] = check_in_time

            # If we're updating check_in_time, also update the date field
            update_data['date'] = check_in_time.date()
        except ValueError:
            return jsonify({"message": "Invalid check-in time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Parse check-out time if provided
    if 'check_out_time' in data:
        try:
            check_out_time = datetime.strptime(data.get('check_out_time'), '%Y-%m-%d %H:%M:%S')
            update_data['check_out_time'] = check_out_time

            # If we're updating check_out_time and not check_in_time, use check_out_time's date
            if 'date' not in update_data:
                update_data['date'] = check_out_time.date()
        except ValueError:
            return jsonify({"message": "Invalid check-out time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # If date is explicitly provided, it will override the date extracted from timestamps
    if 'date' in data:
        date_value = HelperMethods.parse_date(data.get('date'))
        if not date_value:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400
        update_data['date'] = date_value

    # Add other fields to update
    for field in ['employee_id', 'status', 'source', 'source_record_id', 'notes']:
        if field in data:
            update_data[field] = data[field]

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if attendance record exists
        attendance = Attendance.get_attendance_by_id(session, attendance_id)
        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        # We no longer need to update datetime objects with the attendance date
        # since we're now using full datetime values with their own dates

        # If employee_id is being updated, check if the new employee exists
        if 'employee_id' in update_data and update_data['employee_id'] != attendance.employee_id:
            employee = Employee.get_employee_by_id(session, update_data['employee_id'])
            if not employee:
                return jsonify({"message": f"Employee with ID {update_data['employee_id']} not found"}), 404

        # Update the attendance record
        updated_attendance = Attendance.update_attendance(session, attendance_id, **update_data)

        if not updated_attendance:
            return jsonify({"message": "Failed to update attendance record"}), 500

        return jsonify({
            "status": "success",
            "attendance": updated_attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/<attendance_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_attendance(attendance_id):
    """Delete an attendance record."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if attendance record exists
        attendance = Attendance.get_attendance_by_id(session, attendance_id)
        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        # Delete the attendance record
        success = Attendance.delete_attendance(session, attendance_id)

        if not success:
            return jsonify({"message": "Failed to delete attendance record"}), 500

        return jsonify({
            "status": "success",
            "message": "Attendance record deleted successfully"
        }), 200

@attendance_api.route('/api/attendance/statistics', methods=['GET'])
@token_required
def get_attendance_statistics():
    """
    Get attendance statistics for a specific period (weekly, monthly, annual, or custom date range).

    Query Parameters:
    - company_id: Required. The ID of the company to get statistics for.
    - period: Optional. The period type ('weekly', 'monthly', 'annual', 'custom'). Defaults to 'weekly'.
    - date: Optional. Reference date for weekly, monthly, or annual periods in YYYY-MM-DD format. Defaults to today.
    - start_date: Required for custom period. Start date in YYYY-MM-DD format.
    - end_date: Required for custom period. End date in YYYY-MM-DD format.

    Returns:
    - JSON object with attendance statistics for the specified period.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        period = request.args.get('period', 'weekly').lower()
        date_str = request.args.get('date')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse reference date (default to today if not provided)
        reference_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not reference_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Determine date range based on period
        if period == 'weekly':
            start_date, end_date = DateHelper.get_week_date_range(reference_date)
            period_description = f"Week of {start_date.strftime('%Y-%m-%d')}"
        elif period == 'monthly':
            start_date, end_date = DateHelper.get_month_date_range(reference_date)
            period_description = start_date.strftime('%B %Y')
        elif period == 'annual':
            start_date, end_date = DateHelper.get_year_date_range(reference_date)
            period_description = start_date.strftime('%Y')
        elif period == 'custom':
            # For custom period, both start_date and end_date are required
            start_date = HelperMethods.parse_date(start_date_str)
            end_date = HelperMethods.parse_date(end_date_str)

            if not start_date or not end_date:
                return jsonify({"message": "Both start_date and end_date are required for custom period in YYYY-MM-DD format"}), 400

            if start_date > end_date:
                return jsonify({"message": "start_date cannot be after end_date"}), 400

            period_description = f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        else:
            return jsonify({"message": "Invalid period. Use 'weekly', 'monthly', 'annual', or 'custom'"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get all active employees
            all_employees = session.query(Employee).filter(Employee.status == 'active').count()

            # Get all departments
            all_departments = session.query(Department).all()

            # Get all dates in the range first (needed for multiple calculations)
            dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

            # Get attendance records for the date range
            attendance_query = session.query(Attendance).filter(
                Attendance.date >= start_date,
                Attendance.date <= end_date
            )

            # Count by status for the entire period
            present_count = attendance_query.filter(Attendance.status == 'present').count()
            on_leave_count = attendance_query.filter(Attendance.status == 'on leave').count()
            late_count = attendance_query.filter(Attendance.status == 'late').count()

            # Calculate total possible attendance (employees × days)
            total_possible_attendance = all_employees * len(dates_in_range)

            # Calculate absent count (total possible - present - on leave - late)
            absent_count = total_possible_attendance - present_count - on_leave_count - late_count

            # Calculate daily statistics
            daily_stats = []

            # For annual statistics, we'll group by month instead of showing each day
            # to avoid returning too much data
            if period == 'annual':
                # Create monthly statistics instead of daily
                monthly_stats = []
                current_month = None
                month_present = 0
                month_absent = 0
                month_on_leave = 0
                month_late = 0
                month_days = 0

                for current_date in dates_in_range:
                    # If we've moved to a new month, save the previous month's stats
                    if current_month and current_date.month != current_month:
                        # Calculate attendance percentage for the month
                        month_attendance_percentage = 0
                        month_possible_attendance = all_employees * month_days
                        if month_possible_attendance > 0:
                            month_attendance_percentage = round((month_present / month_possible_attendance) * 100, 2)

                        # Add the month's stats
                        month_date = date(current_date.year, current_month, 1)
                        monthly_stats.append({
                            "month": month_date.strftime('%B %Y'),
                            "days_in_month": month_days,
                            "present_count": month_present,
                            "absent_count": month_absent,
                            "on_leave_count": month_on_leave,
                            "late_count": month_late,
                            "attendance_percentage": month_attendance_percentage
                        })

                        # Reset counters for the new month
                        month_present = 0
                        month_absent = 0
                        month_on_leave = 0
                        month_late = 0
                        month_days = 0

                    # Set the current month
                    current_month = current_date.month
                    month_days += 1

                    # Get attendance records for this date
                    day_records = session.query(Attendance).filter(Attendance.date == current_date).all()
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status for this day
                    day_present = sum(1 for status in day_attendance_map.values() if status == 'present')
                    day_on_leave = sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    day_late = sum(1 for status in day_attendance_map.values() if status == 'late')
                    day_absent = all_employees - day_present - day_on_leave - day_late

                    # Add to monthly totals
                    month_present += day_present
                    month_absent += day_absent
                    month_on_leave += day_on_leave
                    month_late += day_late

                # Add the last month's stats
                if current_month:
                    # Calculate attendance percentage for the month
                    month_attendance_percentage = 0
                    month_possible_attendance = all_employees * month_days
                    if month_possible_attendance > 0:
                        month_attendance_percentage = round((month_present / month_possible_attendance) * 100, 2)

                    # Add the month's stats
                    month_date = date(end_date.year, current_month, 1)
                    monthly_stats.append({
                        "month": month_date.strftime('%B %Y'),
                        "days_in_month": month_days,
                        "present_count": month_present,
                        "absent_count": month_absent,
                        "on_leave_count": month_on_leave,
                        "late_count": month_late,
                        "attendance_percentage": month_attendance_percentage
                    })

                # For annual statistics, we'll use the monthly stats instead of daily
                daily_stats = monthly_stats
            elif period != 'annual':
                # For weekly, monthly, and custom periods, calculate daily statistics
                for current_date in dates_in_range:
                    # Get all attendance records for this date
                    day_records = session.query(Attendance).filter(Attendance.date == current_date).all()

                    # Create a mapping of employee_id to their attendance status for this day
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status
                    day_present = sum(1 for status in day_attendance_map.values() if status == 'present')
                    day_on_leave = sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    day_late = sum(1 for status in day_attendance_map.values() if status == 'late')

                    # Count employees without records or with 'absent' status as absent
                    day_absent = all_employees - day_present - day_on_leave - day_late

                    # Calculate attendance percentage for the day
                    day_attendance_percentage = 0
                    if all_employees > 0:
                        day_attendance_percentage = round((day_present / all_employees) * 100, 2)

                    daily_stats.append({
                        "date": current_date.strftime('%Y-%m-%d'),
                        "day_of_week": current_date.strftime('%A'),
                        "present_count": day_present,
                        "absent_count": day_absent,
                        "on_leave_count": day_on_leave,
                        "late_count": day_late,
                        "attendance_percentage": day_attendance_percentage
                    })

            # Calculate department statistics
            department_stats = []

            # First, get employees without department
            no_dept_employees_count = session.query(Employee).filter(
                Employee.department_id.is_(None),
                Employee.status == 'active'
            ).count()

            # If there are employees without department, create a "No Department" category
            if no_dept_employees_count > 0:
                # Get all employee IDs with no department
                no_dept_employee_ids = [str(emp.employee_id) for emp in session.query(Employee.employee_id).filter(
                    Employee.department_id.is_(None),
                    Employee.status == 'active'
                ).all()]

                # Initialize counters
                no_dept_present = 0
                no_dept_on_leave = 0
                no_dept_late = 0

                # For each day in the period, count attendance for employees without department
                for current_date in dates_in_range:
                    # Get attendance records for employees without department on this day
                    day_records = session.query(Attendance).filter(
                        Attendance.employee_id.in_([uuid.UUID(emp_id) for emp_id in no_dept_employee_ids]),
                        Attendance.date == current_date
                    ).all()

                    # Create a mapping of employee_id to their attendance status for this day
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status for this day
                    no_dept_present += sum(1 for status in day_attendance_map.values() if status == 'present')
                    no_dept_on_leave += sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    no_dept_late += sum(1 for status in day_attendance_map.values() if status == 'late')

                # Calculate total possible attendance (employees × days)
                no_dept_possible_attendance = no_dept_employees_count * len(dates_in_range)

                # Calculate absent count (total possible - present - on leave - late)
                no_dept_absent = no_dept_possible_attendance - no_dept_present - no_dept_on_leave - no_dept_late

                # Calculate average attendance for the period
                no_dept_avg_attendance = 0
                if no_dept_possible_attendance > 0:
                    no_dept_avg_attendance = round(no_dept_present / no_dept_possible_attendance * 100, 2)

                # Add "No Department" category to department statistics
                department_stats.append({
                    "department_id": "no_department",
                    "department_name": "No Department",
                    "employee_count": no_dept_employees_count,
                    "present_count": no_dept_present,
                    "absent_count": no_dept_absent,
                    "on_leave_count": no_dept_on_leave,
                    "late_count": no_dept_late,
                    "avg_attendance_percentage": no_dept_avg_attendance
                })

            # Now process regular departments
            for dept in all_departments:
                # Get employees in this department
                dept_employees_count = session.query(Employee).filter(
                    Employee.department_id == dept.department_id,
                    Employee.status == 'active'
                ).count()

                if dept_employees_count > 0:
                    # Get all employee IDs in this department
                    dept_employee_ids = [str(emp.employee_id) for emp in session.query(Employee.employee_id).filter(
                        Employee.department_id == dept.department_id,
                        Employee.status == 'active'
                    ).all()]

                    # Initialize counters
                    dept_present = 0
                    dept_on_leave = 0
                    dept_late = 0

                    # For each day in the period, count attendance for this department
                    for current_date in dates_in_range:
                        # Get attendance records for this department on this day
                        day_records = session.query(Attendance).filter(
                            Attendance.employee_id.in_([uuid.UUID(emp_id) for emp_id in dept_employee_ids]),
                            Attendance.date == current_date
                        ).all()

                        # Create a mapping of employee_id to their attendance status for this day
                        day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                        # Count by status for this day
                        dept_present += sum(1 for status in day_attendance_map.values() if status == 'present')
                        dept_on_leave += sum(1 for status in day_attendance_map.values() if status == 'on leave')
                        dept_late += sum(1 for status in day_attendance_map.values() if status == 'late')

                    # Calculate total possible attendance (employees × days)
                    total_possible_attendance = dept_employees_count * len(dates_in_range)

                    # Calculate absent count (total possible - present - on leave - late)
                    dept_absent = total_possible_attendance - dept_present - dept_on_leave - dept_late

                    # Calculate average attendance for the period
                    avg_attendance = 0
                    if total_possible_attendance > 0:
                        avg_attendance = round(dept_present / total_possible_attendance * 100, 2)

                    department_stats.append({
                        "department_id": str(dept.department_id),
                        "department_name": dept.name,
                        "employee_count": dept_employees_count,
                        "present_count": dept_present,
                        "absent_count": dept_absent,
                        "on_leave_count": dept_on_leave,
                        "late_count": dept_late,
                        "avg_attendance_percentage": avg_attendance
                    })

            # Sort departments by average attendance (descending)
            department_stats.sort(key=lambda x: x['avg_attendance_percentage'], reverse=True)

            # Calculate overall statistics
            total_days = len(dates_in_range)  # dates_in_range is defined earlier in the function

            # Recalculate total_possible_attendance to ensure it's using the correct value
            total_possible_attendance = all_employees * total_days

            attendance_percentage = 0
            if total_possible_attendance > 0:
                attendance_percentage = round((present_count / total_possible_attendance) * 100, 2)

            return jsonify({
                "status": "success",
                "metadata": {
                    "period": period,
                    "period_description": period_description,
                    "start_date": start_date.strftime('%Y-%m-%d'),
                    "end_date": end_date.strftime('%Y-%m-%d'),
                    "total_days": total_days,
                    "total_employees": all_employees
                },
                "summary": {
                    "present_count": present_count,
                    "absent_count": absent_count,
                    "on_leave_count": on_leave_count,
                    "late_count": late_count,
                    "attendance_percentage": attendance_percentage
                },
                # Use appropriate name based on period type
                f"{period}_statistics": daily_stats,
                "department_statistics": department_stats
            }), 200
    except ValueError as e:
        app.logger.error(f"Value error in get_attendance_statistics: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_attendance_statistics: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500

@attendance_api.route('/api/attendance/employee/<employee_id>/statistics', methods=['GET'])
@token_required
def get_employee_attendance_statistics(employee_id):
    """
    Get comprehensive attendance statistics for a specific employee.

    This endpoint provides detailed attendance analytics including:
    - Personal attendance metrics and trends
    - Shift compliance analysis
    - Department and company comparisons
    - Time patterns and productivity insights
    - Behavioral analysis for AI integration

    Query Parameters:
    - company_id: Required. The ID of the company.
    - period: Optional. The period type ('daily', 'weekly', 'monthly', 'annual', 'custom'). Defaults to 'monthly'.
    - date: Optional. Reference date for daily, weekly, monthly, or annual periods in YYYY-MM-DD format. Defaults to today.
    - start_date: Required for custom period. Start date in YYYY-MM-DD format.
    - end_date: Required for custom period. End date in YYYY-MM-DD format.
    - include_comparisons: Optional. Whether to include department/company comparisons. Defaults to true.
    - include_trends: Optional. Whether to include trend analysis. Defaults to true.
    - include_patterns: Optional. Whether to include time patterns analysis. Defaults to true.
    - include_details: Optional. Whether to include detailed daily records. Defaults to false.

    Returns:
    - JSON object with comprehensive employee attendance statistics.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        period = request.args.get('period', 'monthly').lower()
        date_str = request.args.get('date')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        include_comparisons = request.args.get('include_comparisons', 'true').lower() == 'true'
        include_trends = request.args.get('include_trends', 'true').lower() == 'true'
        include_patterns = request.args.get('include_patterns', 'true').lower() == 'true'
        include_details = request.args.get('include_details', 'false').lower() == 'true'

        # Validate period
        valid_periods = ['daily', 'weekly', 'monthly', 'annual', 'custom']
        if period not in valid_periods:
            return jsonify({"message": f"Invalid period. Must be one of: {', '.join(valid_periods)}"}), 400

        # Parse reference date (default to today if not provided)
        reference_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not reference_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Validate that we're not querying too far into the future
        today = datetime.now().date()
        if reference_date > today:
            return jsonify({"message": f"Cannot query future dates. Reference date {reference_date} is after today {today}"}), 400

        # Handle custom period validation
        if period == 'custom':
            if not start_date_str or not end_date_str:
                return jsonify({"message": "start_date and end_date are required for custom period"}), 400

            start_date = HelperMethods.parse_date(start_date_str)
            end_date = HelperMethods.parse_date(end_date_str)

            if not start_date or not end_date:
                return jsonify({"message": "Invalid date format for start_date or end_date. Use YYYY-MM-DD"}), 400

            if start_date > end_date:
                return jsonify({"message": "start_date cannot be after end_date"}), 400

            # Validate custom dates are not in the future
            today = datetime.now().date()
            if end_date > today:
                return jsonify({"message": f"Cannot query future dates. End date {end_date} is after today {today}"}), 400
        else:
            # Calculate date range based on period using existing helper methods
            if period == 'daily':
                start_date = end_date = reference_date
            elif period == 'weekly':
                start_date, end_date = DateHelper.get_week_date_range(reference_date)
            elif period == 'monthly':
                start_date, end_date = DateHelper.get_month_date_range(reference_date)
            elif period == 'annual':
                start_date, end_date = DateHelper.get_year_date_range(reference_date)

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get employee information
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

            # Get employee's attendance records for the period
            attendance_records = session.query(Attendance).filter(
                Attendance.employee_id == employee_id,
                Attendance.date.between(start_date, end_date)
            ).order_by(Attendance.date).all()

            # Get employee's shift assignments for the period
            from application.Models.employees import EmployeeShift, Shift
            shift_assignments = session.query(EmployeeShift).join(Shift).filter(
                EmployeeShift.employee_id == employee_id,
                EmployeeShift.is_active == True,
                EmployeeShift.effective_start_date <= end_date,
                (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
            ).all()

            # Calculate working days based on shifts or default to weekdays
            dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)
            working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
            total_days = len(dates_in_range)

            # Debug logging for working days calculation
            app.logger.info(f"Employee {employee_id} statistics calculation:")
            app.logger.info(f"  - Date range: {start_date} to {end_date}")
            app.logger.info(f"  - Total days in range: {total_days}")
            app.logger.info(f"  - Calculated working days: {working_days}")
            app.logger.info(f"  - Shift assignments count: {len(shift_assignments)}")
            app.logger.info(f"  - Attendance records count: {len(attendance_records)}")

            # Attendance counts and categorization
            present_records = [r for r in attendance_records if r.status in ['present', 'late']]
            late_records = [r for r in attendance_records if r.status == 'late']
            on_leave_records = [r for r in attendance_records if r.status == 'on leave']

            present_days = len(present_records)
            late_days = len(late_records)
            on_leave_days = len(on_leave_records)

            # Calculate absent days properly - ensure it's never negative
            absent_days = max(0, working_days - present_days - on_leave_days)

            # Time calculations
            total_hours = sum([r.total_hours for r in attendance_records if r.total_hours])
            average_daily_hours = total_hours / present_days if present_days > 0 else 0

            # Calculate expected hours based on shifts
            expected_hours = _calculate_expected_hours_for_employee(dates_in_range, shift_assignments, working_days)
            overtime_hours = max(0, total_hours - expected_hours) if expected_hours > 0 else 0
            undertime_hours = max(0, expected_hours - total_hours) if expected_hours > 0 else 0

            # Performance metrics
            attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
            punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0
            efficiency_rate = (total_hours / expected_hours * 100) if expected_hours > 0 else 0

            # Build employee info with enhanced details
            employee_info = {
                "employee_id": str(employee.employee_id),
                "first_name": employee.first_name,
                "last_name": employee.last_name,
                "full_name": f"{employee.first_name} {employee.last_name}",
                "email": employee.email,
                "phone_number": employee.phone_number,
                "id_number": employee.id_number,
                "position": employee.position,
                "hire_date": employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None,
                "status": employee.status,
                "department": {
                    "department_id": str(employee.department_id) if employee.department_id else None,
                    "name": employee.department.name if employee.department else "No Department"
                },
                "tenure_days": (datetime.now().date() - employee.hire_date).days if employee.hire_date else None
            }

            # Build period info
            period_info = {
                "type": period,
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "total_days": total_days,
                "working_days": working_days,
                "reference_date": reference_date.strftime('%Y-%m-%d') if period != 'custom' else None,
                "period_description": _get_period_description(period, start_date, end_date, reference_date)
            }

            # Core attendance statistics
            attendance_stats = {
                "present_days": present_days,
                "absent_days": absent_days,
                "late_days": late_days,
                "on_leave_days": on_leave_days,
                "attendance_rate": round(attendance_rate, 2),
                "punctuality_rate": round(punctuality_rate, 2),
                "consistency_score": _calculate_consistency_score(attendance_records, working_days)
            }

            # Time and productivity statistics
            time_stats = {
                "total_hours": round(total_hours, 2),
                "expected_hours": round(expected_hours, 2),
                "average_daily_hours": round(average_daily_hours, 2),
                "overtime_hours": round(overtime_hours, 2),
                "undertime_hours": round(undertime_hours, 2),
                "efficiency_rate": round(efficiency_rate, 2),
                "productivity_score": _calculate_productivity_score(total_hours, expected_hours, punctuality_rate)
            }

            # Shift compliance analysis
            shift_compliance = _analyze_shift_compliance(attendance_records, shift_assignments, dates_in_range)

            # Time patterns analysis (if requested)
            time_patterns = {}
            if include_patterns:
                time_patterns = _analyze_time_patterns(attendance_records, dates_in_range)

            # Department and company comparisons (if requested)
            comparisons = {}
            if include_comparisons:
                comparisons = _get_employee_comparisons(session, employee, attendance_rate, punctuality_rate,
                                                      efficiency_rate, start_date, end_date)

            # Trend analysis (if requested)
            trends = {}
            if include_trends and period in ['monthly', 'annual']:
                trends = _analyze_attendance_trends(session, employee_id, start_date, end_date, period)

            # Detailed daily records (if requested)
            daily_details = []
            if include_details:
                daily_details = _get_daily_attendance_details(attendance_records, dates_in_range, shift_assignments)

            # Build comprehensive response
            response_data = {
                "status": "success",
                "employee": employee_info,
                "period": period_info,
                "statistics": {
                    "attendance": attendance_stats,
                    "time": time_stats,
                    "shift_compliance": shift_compliance
                }
            }

            # Add optional sections based on parameters
            if include_patterns:
                response_data["time_patterns"] = time_patterns

            if include_comparisons:
                response_data["comparisons"] = comparisons

            if include_trends:
                response_data["trends"] = trends

            if include_details:
                response_data["daily_details"] = daily_details

            # Add AI-ready insights
            response_data["ai_insights"] = _generate_ai_insights(
                attendance_stats, time_stats, shift_compliance, time_patterns, comparisons
            )

            return jsonify(response_data), 200

    except ValueError as e:
        app.logger.error(f"Value error in get_employee_attendance_statistics: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_employee_attendance_statistics: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500


# Helper functions for employee statistics
def _calculate_working_days_for_employee(dates_in_range, shift_assignments):
    """Calculate working days for an employee based on their shift assignments."""
    if not shift_assignments:
        # Default to weekdays if no shifts assigned (Monday=0 to Friday=4)
        return len([d for d in dates_in_range if d.weekday() < 5])

    working_days = 0
    for date in dates_in_range:
        is_working_day = False

        # Check if employee has an active shift assignment on this day
        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):

                shift = assignment.shift
                if shift and hasattr(shift, 'working_days') and shift.working_days:
                    # Parse working days from shift (e.g., "monday,tuesday,wednesday,thursday,friday")
                    day_name = date.strftime('%A').lower()
                    working_days_list = [day.strip().lower() for day in shift.working_days.split(',')]
                    if day_name in working_days_list:
                        is_working_day = True
                        break
                else:
                    # If shift exists but no working_days specified, default to weekdays
                    if date.weekday() < 5:  # Monday=0 to Friday=4
                        is_working_day = True
                        break

        # If no shift assignment found, check if it's a weekday
        if not is_working_day and not shift_assignments:
            if date.weekday() < 5:
                is_working_day = True

        # If still no shift assignment but we have assignments, default to weekdays
        if not is_working_day and shift_assignments:
            # Check if any assignment covers this period (even if not this specific date)
            has_any_assignment = any(
                assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)
                for assignment in shift_assignments
            )
            if has_any_assignment and date.weekday() < 5:
                is_working_day = True

        if is_working_day:
            working_days += 1

    return working_days


def _calculate_expected_hours_for_employee(dates_in_range, shift_assignments, working_days):
    """Calculate expected working hours for an employee based on their shifts."""
    if not shift_assignments:
        # Default to 8 hours per working day
        return working_days * 8.0

    total_expected_hours = 0.0

    for date in dates_in_range:
        # Skip weekends if no shift assignment covers this date
        if date.weekday() >= 5:  # Saturday=5, Sunday=6
            continue

        day_hours_added = False

        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):

                shift = assignment.shift
                if shift:
                    # Use custom times if available, otherwise use shift defaults
                    start_time = assignment.custom_start_time or shift.start_time
                    end_time = assignment.custom_end_time or shift.end_time
                    break_duration = assignment.custom_break_duration or shift.break_duration or 0

                    if start_time and end_time:
                        # Calculate hours for this day
                        start_datetime = datetime.combine(date, start_time)
                        end_datetime = datetime.combine(date, end_time)

                        # Handle overnight shifts
                        if end_time < start_time:
                            end_datetime += timedelta(days=1)

                        shift_hours = (end_datetime - start_datetime).total_seconds() / 3600
                        shift_hours -= break_duration / 60.0  # Convert break minutes to hours
                        total_expected_hours += max(0, shift_hours)
                        day_hours_added = True
                        break
                else:
                    # Default to 8 hours if shift exists but no details
                    total_expected_hours += 8.0
                    day_hours_added = True
                    break

        # If no shift assignment found for this weekday, add default 8 hours
        if not day_hours_added and date.weekday() < 5:
            total_expected_hours += 8.0

    return total_expected_hours


def _get_period_description(period, start_date, end_date, reference_date):
    """Generate a human-readable description of the period."""
    if period == 'daily':
        return start_date.strftime('%A, %B %d, %Y')
    elif period == 'weekly':
        return f"Week of {start_date.strftime('%B %d, %Y')}"
    elif period == 'monthly':
        return start_date.strftime('%B %Y')
    elif period == 'annual':
        return start_date.strftime('%Y')
    elif period == 'custom':
        return f"{start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    else:
        return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"


def _calculate_consistency_score(attendance_records, working_days):
    """Calculate a consistency score based on attendance patterns."""
    if working_days == 0:
        return 0.0

    # Create a list of attendance status for each working day
    attendance_pattern = []
    record_dates = {record.date: record.status for record in attendance_records}

    # Calculate consistency based on regularity of attendance
    present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
    consistency_base = (present_days / working_days) * 100

    # Penalize for irregular patterns (many late days)
    late_days = len([r for r in attendance_records if r.status == 'late'])
    late_penalty = (late_days / working_days) * 20 if working_days > 0 else 0

    consistency_score = max(0, consistency_base - late_penalty)
    return round(consistency_score, 2)


def _calculate_productivity_score(total_hours, expected_hours, punctuality_rate):
    """Calculate a productivity score based on hours worked and punctuality."""
    if expected_hours == 0:
        return 0.0

    # Base score from hours efficiency
    hours_efficiency = min(100, (total_hours / expected_hours) * 100)

    # Weight: 70% hours efficiency, 30% punctuality
    productivity_score = (hours_efficiency * 0.7) + (punctuality_rate * 0.3)

    return round(productivity_score, 2)


def _analyze_shift_compliance(attendance_records, shift_assignments, dates_in_range):
    """Analyze how well the employee complies with their assigned shifts."""
    if not shift_assignments:
        return {
            "has_shifts": False,
            "compliance_rate": 0.0,
            "early_arrivals": 0,
            "late_arrivals": 0,
            "early_departures": 0,
            "late_departures": 0,
            "shift_violations": []
        }

    compliance_data = {
        "has_shifts": True,
        "total_shift_days": 0,
        "compliant_days": 0,
        "early_arrivals": 0,
        "late_arrivals": 0,
        "early_departures": 0,
        "late_departures": 0,
        "shift_violations": []
    }

    # Create a map of attendance records by date
    attendance_map = {record.date: record for record in attendance_records}

    for date in dates_in_range:
        # Find the shift assignment for this date
        current_assignment = None
        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):
                current_assignment = assignment
                break

        if current_assignment and date in attendance_map:
            compliance_data["total_shift_days"] += 1
            record = attendance_map[date]
            shift = current_assignment.shift

            # Get expected times (custom or shift default)
            expected_start = current_assignment.custom_start_time or shift.start_time
            expected_end = current_assignment.custom_end_time or shift.end_time

            if expected_start and expected_end and record.check_in_time and record.check_out_time:
                # Check arrival compliance
                if record.check_in_time <= expected_start:
                    compliance_data["early_arrivals"] += 1
                elif record.check_in_time > expected_start:
                    compliance_data["late_arrivals"] += 1
                    compliance_data["shift_violations"].append({
                        "date": date.strftime('%Y-%m-%d'),
                        "type": "late_arrival",
                        "expected": expected_start.strftime('%H:%M'),
                        "actual": record.check_in_time.strftime('%H:%M'),
                        "minutes_late": int((record.check_in_time.hour * 60 + record.check_in_time.minute) -
                                          (expected_start.hour * 60 + expected_start.minute))
                    })

                # Check departure compliance
                if record.check_out_time >= expected_end:
                    compliance_data["late_departures"] += 1
                elif record.check_out_time < expected_end:
                    compliance_data["early_departures"] += 1
                    compliance_data["shift_violations"].append({
                        "date": date.strftime('%Y-%m-%d'),
                        "type": "early_departure",
                        "expected": expected_end.strftime('%H:%M'),
                        "actual": record.check_out_time.strftime('%H:%M'),
                        "minutes_early": int((expected_end.hour * 60 + expected_end.minute) -
                                           (record.check_out_time.hour * 60 + record.check_out_time.minute))
                    })

                # Count as compliant if both arrival and departure are within tolerance
                if (record.check_in_time <= expected_start or
                    (record.check_in_time.hour * 60 + record.check_in_time.minute) -
                    (expected_start.hour * 60 + expected_start.minute) <= 15):  # 15 min tolerance
                    if record.check_out_time >= expected_end:
                        compliance_data["compliant_days"] += 1

    # Calculate compliance rate
    if compliance_data["total_shift_days"] > 0:
        compliance_data["compliance_rate"] = round(
            (compliance_data["compliant_days"] / compliance_data["total_shift_days"]) * 100, 2
        )
    else:
        compliance_data["compliance_rate"] = 0.0

    return compliance_data


def _analyze_time_patterns(attendance_records, dates_in_range):
    """Analyze time-based patterns in employee attendance."""
    if not attendance_records:
        return {
            "average_check_in_time": None,
            "average_check_out_time": None,
            "most_common_check_in_hour": None,
            "most_common_check_out_hour": None,
            "day_of_week_patterns": {},
            "time_consistency": 0.0
        }

    # Filter records with valid times
    valid_records = [r for r in attendance_records if r.check_in_time and r.check_out_time]

    if not valid_records:
        return {
            "average_check_in_time": None,
            "average_check_out_time": None,
            "most_common_check_in_hour": None,
            "most_common_check_out_hour": None,
            "day_of_week_patterns": {},
            "time_consistency": 0.0
        }

    # Calculate average times
    total_check_in_minutes = sum(r.check_in_time.hour * 60 + r.check_in_time.minute for r in valid_records)
    total_check_out_minutes = sum(r.check_out_time.hour * 60 + r.check_out_time.minute for r in valid_records)

    avg_check_in_minutes = total_check_in_minutes // len(valid_records)
    avg_check_out_minutes = total_check_out_minutes // len(valid_records)

    avg_check_in_time = f"{avg_check_in_minutes // 60:02d}:{avg_check_in_minutes % 60:02d}"
    avg_check_out_time = f"{avg_check_out_minutes // 60:02d}:{avg_check_out_minutes % 60:02d}"

    # Find most common hours
    check_in_hours = [r.check_in_time.hour for r in valid_records]
    check_out_hours = [r.check_out_time.hour for r in valid_records]

    from collections import Counter
    most_common_check_in = Counter(check_in_hours).most_common(1)
    most_common_check_out = Counter(check_out_hours).most_common(1)

    # Day of week patterns
    day_patterns = {}
    for record in valid_records:
        day_name = record.date.strftime('%A')
        if day_name not in day_patterns:
            day_patterns[day_name] = {
                "count": 0,
                "avg_check_in": 0,
                "avg_check_out": 0,
                "total_hours": 0.0
            }

        day_patterns[day_name]["count"] += 1
        day_patterns[day_name]["avg_check_in"] += record.check_in_time.hour * 60 + record.check_in_time.minute
        day_patterns[day_name]["avg_check_out"] += record.check_out_time.hour * 60 + record.check_out_time.minute
        if record.total_hours:
            day_patterns[day_name]["total_hours"] += record.total_hours

    # Calculate averages for each day
    for day in day_patterns:
        count = day_patterns[day]["count"]
        if count > 0:
            avg_in = day_patterns[day]["avg_check_in"] // count
            avg_out = day_patterns[day]["avg_check_out"] // count
            day_patterns[day]["avg_check_in"] = f"{avg_in // 60:02d}:{avg_in % 60:02d}"
            day_patterns[day]["avg_check_out"] = f"{avg_out // 60:02d}:{avg_out % 60:02d}"
            day_patterns[day]["avg_hours"] = round(day_patterns[day]["total_hours"] / count, 2)

    # Calculate time consistency (standard deviation of check-in times)
    if len(valid_records) > 1:
        check_in_minutes = [r.check_in_time.hour * 60 + r.check_in_time.minute for r in valid_records]
        mean_minutes = sum(check_in_minutes) / len(check_in_minutes)
        variance = sum((x - mean_minutes) ** 2 for x in check_in_minutes) / len(check_in_minutes)
        std_dev = variance ** 0.5
        # Convert to consistency score (lower std_dev = higher consistency)
        time_consistency = max(0, 100 - (std_dev / 2))  # Normalize to 0-100 scale
    else:
        time_consistency = 100.0

    return {
        "average_check_in_time": avg_check_in_time,
        "average_check_out_time": avg_check_out_time,
        "most_common_check_in_hour": most_common_check_in[0][0] if most_common_check_in else None,
        "most_common_check_out_hour": most_common_check_out[0][0] if most_common_check_out else None,
        "day_of_week_patterns": day_patterns,
        "time_consistency": round(time_consistency, 2)
    }


def _get_employee_comparisons(session, employee, attendance_rate, punctuality_rate, efficiency_rate, start_date, end_date):
    """Get department and company-wide comparisons for the employee."""
    comparisons = {
        "department": {
            "name": employee.department.name if employee.department else "No Department",
            "employee_count": 0,
            "avg_attendance_rate": 0.0,
            "avg_punctuality_rate": 0.0,
            "avg_efficiency_rate": 0.0,
            "employee_rank": 0
        },
        "company": {
            "total_employees": 0,
            "avg_attendance_rate": 0.0,
            "avg_punctuality_rate": 0.0,
            "avg_efficiency_rate": 0.0,
            "employee_rank": 0
        }
    }

    try:
        # Get department statistics
        if employee.department_id:
            dept_employees = session.query(Employee).filter(
                Employee.department_id == employee.department_id,
                Employee.status == 'active'
            ).all()

            if dept_employees:
                dept_stats = _calculate_department_averages(session, dept_employees, start_date, end_date)
                comparisons["department"].update(dept_stats)

                # Calculate employee rank in department
                dept_attendance_rates = [dept_stats.get("employee_rates", {}).get(str(emp.employee_id), 0)
                                       for emp in dept_employees]
                dept_attendance_rates.sort(reverse=True)
                employee_rank = dept_attendance_rates.index(attendance_rate) + 1 if attendance_rate in dept_attendance_rates else len(dept_attendance_rates)
                comparisons["department"]["employee_rank"] = employee_rank

        # Get company statistics
        all_employees = session.query(Employee).filter(Employee.status == 'active').all()
        if all_employees:
            company_stats = _calculate_company_averages(session, all_employees, start_date, end_date)
            comparisons["company"].update(company_stats)

            # Calculate employee rank in company
            company_attendance_rates = [company_stats.get("employee_rates", {}).get(str(emp.employee_id), 0)
                                      for emp in all_employees]
            company_attendance_rates.sort(reverse=True)
            employee_rank = company_attendance_rates.index(attendance_rate) + 1 if attendance_rate in company_attendance_rates else len(company_attendance_rates)
            comparisons["company"]["employee_rank"] = employee_rank

    except Exception as e:
        app.logger.error(f"Error calculating comparisons: {str(e)}")

    return comparisons


def _calculate_department_averages(session, dept_employees, start_date, end_date):
    """Calculate average statistics for a department."""
    if not dept_employees:
        return {"employee_count": 0, "avg_attendance_rate": 0.0, "avg_punctuality_rate": 0.0, "avg_efficiency_rate": 0.0}

    total_attendance_rate = 0.0
    total_punctuality_rate = 0.0
    total_efficiency_rate = 0.0
    employee_rates = {}

    for emp in dept_employees:
        # Get employee's attendance for the period
        attendance_records = session.query(Attendance).filter(
            Attendance.employee_id == emp.employee_id,
            Attendance.date.between(start_date, end_date)
        ).all()

        # Calculate basic rates for this employee
        working_days = len(DateHelper.get_dates_in_range(start_date, end_date))  # Simplified
        present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_days = len([r for r in attendance_records if r.status == 'late'])

        emp_attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
        emp_punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0

        total_attendance_rate += emp_attendance_rate
        total_punctuality_rate += emp_punctuality_rate
        employee_rates[str(emp.employee_id)] = emp_attendance_rate

    count = len(dept_employees)
    return {
        "employee_count": count,
        "avg_attendance_rate": round(total_attendance_rate / count, 2) if count > 0 else 0.0,
        "avg_punctuality_rate": round(total_punctuality_rate / count, 2) if count > 0 else 0.0,
        "avg_efficiency_rate": 0.0,  # Simplified for now
        "employee_rates": employee_rates
    }


def _calculate_company_averages(session, all_employees, start_date, end_date):
    """Calculate average statistics for the entire company."""
    if not all_employees:
        return {"total_employees": 0, "avg_attendance_rate": 0.0, "avg_punctuality_rate": 0.0, "avg_efficiency_rate": 0.0}

    total_attendance_rate = 0.0
    total_punctuality_rate = 0.0
    employee_rates = {}

    for emp in all_employees:
        # Get employee's attendance for the period
        attendance_records = session.query(Attendance).filter(
            Attendance.employee_id == emp.employee_id,
            Attendance.date.between(start_date, end_date)
        ).all()

        # Calculate basic rates for this employee
        working_days = len(DateHelper.get_dates_in_range(start_date, end_date))  # Simplified
        present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_days = len([r for r in attendance_records if r.status == 'late'])

        emp_attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
        emp_punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0

        total_attendance_rate += emp_attendance_rate
        total_punctuality_rate += emp_punctuality_rate
        employee_rates[str(emp.employee_id)] = emp_attendance_rate

    count = len(all_employees)
    return {
        "total_employees": count,
        "avg_attendance_rate": round(total_attendance_rate / count, 2) if count > 0 else 0.0,
        "avg_punctuality_rate": round(total_punctuality_rate / count, 2) if count > 0 else 0.0,
        "avg_efficiency_rate": 0.0,  # Simplified for now
        "employee_rates": employee_rates
    }


def _analyze_attendance_trends(session, employee_id, start_date, end_date, period):
    """Analyze attendance trends over time for the employee."""
    trends = {
        "trend_direction": "stable",
        "trend_strength": 0.0,
        "period_comparison": [],
        "improvement_areas": [],
        "recommendations": []
    }

    try:
        if period == 'monthly':
            # Compare weeks within the month
            weeks = []
            current_date = start_date
            while current_date <= end_date:
                week_start = current_date
                week_end = min(current_date + timedelta(days=6), end_date)

                week_records = session.query(Attendance).filter(
                    Attendance.employee_id == employee_id,
                    Attendance.date.between(week_start, week_end)
                ).all()

                week_working_days = len(DateHelper.get_dates_in_range(week_start, week_end))
                week_present_days = len([r for r in week_records if r.status in ['present', 'late']])
                week_attendance_rate = (week_present_days / week_working_days * 100) if week_working_days > 0 else 0

                weeks.append({
                    "period": f"Week {len(weeks) + 1}",
                    "start_date": week_start.strftime('%Y-%m-%d'),
                    "end_date": week_end.strftime('%Y-%m-%d'),
                    "attendance_rate": round(week_attendance_rate, 2),
                    "present_days": week_present_days,
                    "working_days": week_working_days
                })

                current_date += timedelta(days=7)

            trends["period_comparison"] = weeks

            # Calculate trend direction
            if len(weeks) >= 2:
                rates = [w["attendance_rate"] for w in weeks]
                if rates[-1] > rates[0]:
                    trends["trend_direction"] = "improving"
                elif rates[-1] < rates[0]:
                    trends["trend_direction"] = "declining"

                # Calculate trend strength (simple linear correlation)
                if len(rates) > 2:
                    x_values = list(range(len(rates)))
                    correlation = _calculate_correlation(x_values, rates)
                    trends["trend_strength"] = abs(correlation)

        elif period == 'annual':
            # Compare months within the year
            months = []
            current_month = start_date.replace(day=1)

            while current_month <= end_date:
                # Get last day of current month
                if current_month.month == 12:
                    month_end = current_month.replace(year=current_month.year + 1, month=1, day=1) - timedelta(days=1)
                else:
                    month_end = current_month.replace(month=current_month.month + 1, day=1) - timedelta(days=1)

                month_end = min(month_end, end_date)

                month_records = session.query(Attendance).filter(
                    Attendance.employee_id == employee_id,
                    Attendance.date.between(current_month, month_end)
                ).all()

                month_working_days = len(DateHelper.get_dates_in_range(current_month, month_end))
                month_present_days = len([r for r in month_records if r.status in ['present', 'late']])
                month_attendance_rate = (month_present_days / month_working_days * 100) if month_working_days > 0 else 0

                months.append({
                    "period": current_month.strftime('%B %Y'),
                    "start_date": current_month.strftime('%Y-%m-%d'),
                    "end_date": month_end.strftime('%Y-%m-%d'),
                    "attendance_rate": round(month_attendance_rate, 2),
                    "present_days": month_present_days,
                    "working_days": month_working_days
                })

                # Move to next month
                if current_month.month == 12:
                    current_month = current_month.replace(year=current_month.year + 1, month=1)
                else:
                    current_month = current_month.replace(month=current_month.month + 1)

            trends["period_comparison"] = months

    except Exception as e:
        app.logger.error(f"Error analyzing trends: {str(e)}")

    return trends


def _calculate_correlation(x_values, y_values):
    """Calculate simple correlation coefficient."""
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0

    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)
    sum_y2 = sum(y * y for y in y_values)

    numerator = n * sum_xy - sum_x * sum_y
    denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5

    if denominator == 0:
        return 0.0

    return numerator / denominator


def _get_daily_attendance_details(attendance_records, dates_in_range, shift_assignments):
    """Get detailed daily attendance records with shift information."""
    daily_details = []
    attendance_map = {record.date: record for record in attendance_records}

    for date in dates_in_range:
        # Find shift assignment for this date
        current_shift = None
        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):
                current_shift = assignment
                break

        # Get attendance record for this date
        record = attendance_map.get(date)

        detail = {
            "date": date.strftime('%Y-%m-%d'),
            "day_of_week": date.strftime('%A'),
            "status": record.status if record else "absent",
            "check_in_time": record.check_in_time.strftime('%H:%M:%S') if record and record.check_in_time else None,
            "check_out_time": record.check_out_time.strftime('%H:%M:%S') if record and record.check_out_time else None,
            "total_hours": record.total_hours if record else 0.0,
            "break_duration": record.break_duration if record else 0,
            "overtime_hours": record.overtime_hours if record else 0.0,
            "shift_info": {
                "has_shift": current_shift is not None,
                "shift_name": current_shift.shift.name if current_shift and current_shift.shift else None,
                "expected_start": current_shift.custom_start_time.strftime('%H:%M:%S') if current_shift and current_shift.custom_start_time
                                else current_shift.shift.start_time.strftime('%H:%M:%S') if current_shift and current_shift.shift and current_shift.shift.start_time else None,
                "expected_end": current_shift.custom_end_time.strftime('%H:%M:%S') if current_shift and current_shift.custom_end_time
                              else current_shift.shift.end_time.strftime('%H:%M:%S') if current_shift and current_shift.shift and current_shift.shift.end_time else None,
                "expected_hours": _calculate_expected_hours_for_single_day(current_shift) if current_shift else 8.0
            },
            "compliance": {
                "on_time_arrival": False,
                "on_time_departure": False,
                "hours_compliance": False,
                "overall_compliant": False
            }
        }

        # Calculate compliance if we have both record and shift
        if record and current_shift and current_shift.shift:
            expected_start = current_shift.custom_start_time or current_shift.shift.start_time
            expected_end = current_shift.custom_end_time or current_shift.shift.end_time

            if expected_start and expected_end and record.check_in_time and record.check_out_time:
                detail["compliance"]["on_time_arrival"] = record.check_in_time <= expected_start
                detail["compliance"]["on_time_departure"] = record.check_out_time >= expected_end

                expected_hours = detail["shift_info"]["expected_hours"]
                detail["compliance"]["hours_compliance"] = record.total_hours >= (expected_hours * 0.9) if record.total_hours else False

                detail["compliance"]["overall_compliant"] = (
                    detail["compliance"]["on_time_arrival"] and
                    detail["compliance"]["on_time_departure"] and
                    detail["compliance"]["hours_compliance"]
                )

        daily_details.append(detail)

    return daily_details


def _calculate_expected_hours_for_single_day(shift_assignment):
    """Calculate expected hours for a single day based on shift assignment."""
    if not shift_assignment or not shift_assignment.shift:
        return 8.0

    start_time = shift_assignment.custom_start_time or shift_assignment.shift.start_time
    end_time = shift_assignment.custom_end_time or shift_assignment.shift.end_time
    break_duration = shift_assignment.custom_break_duration or shift_assignment.shift.break_duration or 0

    if not start_time or not end_time:
        return 8.0

    # Calculate hours
    start_minutes = start_time.hour * 60 + start_time.minute
    end_minutes = end_time.hour * 60 + end_time.minute

    # Handle overnight shifts
    if end_minutes < start_minutes:
        end_minutes += 24 * 60

    total_minutes = end_minutes - start_minutes - break_duration
    return max(0, total_minutes / 60.0)


def _generate_ai_insights(attendance_stats, time_stats, shift_compliance, time_patterns, comparisons):
    """Generate AI-ready insights and recommendations for the employee."""
    insights = {
        "performance_summary": {
            "overall_score": 0.0,
            "strengths": [],
            "areas_for_improvement": [],
            "risk_level": "low"
        },
        "behavioral_patterns": {
            "consistency_level": "high",
            "punctuality_trend": "stable",
            "work_hours_pattern": "regular",
            "anomalies": []
        },
        "recommendations": {
            "immediate_actions": [],
            "long_term_goals": [],
            "manager_actions": []
        },
        "predictive_indicators": {
            "attendance_risk": "low",
            "burnout_risk": "low",
            "performance_trend": "stable"
        }
    }

    # Calculate overall performance score
    attendance_weight = 0.4
    punctuality_weight = 0.3
    efficiency_weight = 0.3

    overall_score = (
        attendance_stats["attendance_rate"] * attendance_weight +
        attendance_stats["punctuality_rate"] * punctuality_weight +
        time_stats["efficiency_rate"] * efficiency_weight
    )
    insights["performance_summary"]["overall_score"] = round(overall_score, 2)

    # Determine strengths and areas for improvement
    if attendance_stats["attendance_rate"] >= 95:
        insights["performance_summary"]["strengths"].append("Excellent attendance record")
    elif attendance_stats["attendance_rate"] < 85:
        insights["performance_summary"]["areas_for_improvement"].append("Improve attendance consistency")

    if attendance_stats["punctuality_rate"] >= 90:
        insights["performance_summary"]["strengths"].append("Very punctual")
    elif attendance_stats["punctuality_rate"] < 75:
        insights["performance_summary"]["areas_for_improvement"].append("Improve punctuality")

    if time_stats["efficiency_rate"] >= 100:
        insights["performance_summary"]["strengths"].append("Meets or exceeds expected work hours")
    elif time_stats["efficiency_rate"] < 90:
        insights["performance_summary"]["areas_for_improvement"].append("Increase productive work hours")

    # Determine risk level
    if attendance_stats["attendance_rate"] < 80 or attendance_stats["punctuality_rate"] < 70:
        insights["performance_summary"]["risk_level"] = "high"
        insights["predictive_indicators"]["attendance_risk"] = "high"
    elif attendance_stats["attendance_rate"] < 90 or attendance_stats["punctuality_rate"] < 85:
        insights["performance_summary"]["risk_level"] = "medium"
        insights["predictive_indicators"]["attendance_risk"] = "medium"

    # Analyze behavioral patterns
    if time_patterns.get("time_consistency", 0) >= 80:
        insights["behavioral_patterns"]["consistency_level"] = "high"
    elif time_patterns.get("time_consistency", 0) >= 60:
        insights["behavioral_patterns"]["consistency_level"] = "medium"
    else:
        insights["behavioral_patterns"]["consistency_level"] = "low"

    # Generate recommendations
    if attendance_stats["attendance_rate"] < 90:
        insights["recommendations"]["immediate_actions"].append("Schedule one-on-one meeting to discuss attendance concerns")
        insights["recommendations"]["manager_actions"].append("Implement attendance improvement plan")

    if attendance_stats["punctuality_rate"] < 85:
        insights["recommendations"]["immediate_actions"].append("Review and adjust work schedule if needed")
        insights["recommendations"]["long_term_goals"].append("Achieve 95% punctuality rate within 3 months")

    if time_stats["efficiency_rate"] < 90:
        insights["recommendations"]["immediate_actions"].append("Analyze workload and time management practices")
        insights["recommendations"]["long_term_goals"].append("Optimize work processes to meet expected hours")

    # Shift compliance insights
    if shift_compliance.get("has_shifts") and shift_compliance.get("compliance_rate", 0) < 85:
        insights["recommendations"]["immediate_actions"].append("Review shift requirements and provide additional training")
        insights["behavioral_patterns"]["anomalies"].append("Frequent shift compliance violations")

    # Overtime analysis
    if time_stats["overtime_hours"] > (time_stats["expected_hours"] * 0.2):  # More than 20% overtime
        insights["predictive_indicators"]["burnout_risk"] = "medium"
        insights["recommendations"]["manager_actions"].append("Monitor workload to prevent burnout")

    return insights

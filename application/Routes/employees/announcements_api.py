from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from datetime import datetime, date
from application.Models.employees.announcement import Announcement
from application.Models.employees.announcement_read import AnnouncementRead
from application.Models.employees.employee import Employee
from application.Models.employees.department import Department
from application.Models.employees.company_user import CompanyUser
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.decorators.subscription_required import subscription_required
from application.Helpers.helper_methods import HelperMethods

announcements_api = Blueprint('announcements_api', __name__)

def get_user_context():
    """Helper function to extract user and company context - simplified like other endpoints."""
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR), get company_id from request (like other endpoints)
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)

    if not company_id:
        return None, None, "Company ID is required"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None

# ==================== HR/Admin Announcement Management ====================

@announcements_api.route('/api/announcements', methods=['POST'])
@token_required
@roles_required('hr', 'admin')
@subscription_required('announcements', increment_usage=True)
def create_announcement():
    """
    Create a new announcement.
    
    Request Body:
    - title: Required. The title of the announcement.
    - content: Required. The content of the announcement.
    - summary: Optional. Brief summary for previews.
    - announcement_type: Optional. Type of announcement (GENERAL, POLICY, EVENT, etc.). Default: GENERAL.
    - priority: Optional. Priority level (LOW, MEDIUM, HIGH, URGENT). Default: MEDIUM.
    - category: Optional. Custom category.
    - tags: Optional. Array of tags.
    - target_audience: Optional. Target audience (ALL, DEPARTMENT_SPECIFIC, CUSTOM). Default: ALL.
    - department_ids: Optional. Array of department IDs (required if target_audience is DEPARTMENT_SPECIFIC).
    - employee_ids: Optional. Array of employee IDs (required if target_audience is CUSTOM).
    - publish_date: Optional. Scheduled publish date (ISO format).
    - expiry_date: Optional. Expiry date (ISO format).
    - is_pinned: Optional. Whether to pin the announcement. Default: false.
    - allows_comments: Optional. Whether to allow comments. Default: false.
    - requires_acknowledgment: Optional. Whether to require acknowledgment. Default: false.
    - attachment_urls: Optional. Array of attachment URLs.
    """
    try:
        data = request.get_json()

        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            app.logger.error(f"User context error: {error_msg}")
            return jsonify({"message": error_msg}), 401

        # Debug logging
        app.logger.info(f"Creating announcement - Company ID: {company_id}, User ID: {user_id}")
        app.logger.info(f"Request data: {data}")

        # Validate required fields
        required_fields = ['title', 'content']
        for field in required_fields:
            if not data.get(field):
                app.logger.error(f"Missing required field: {field}")
                return jsonify({"message": f"{field} is required"}), 400

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            app.logger.error(f"Company not found: {company_id}")
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name
        app.logger.info(f"Using database: {database_name}")

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Validate department IDs if targeting specific departments
            if data.get('target_audience') == 'DEPARTMENT_SPECIFIC':
                dept_ids = data.get('department_ids', [])
                if not dept_ids:
                    return jsonify({"message": "department_ids is required when target_audience is DEPARTMENT_SPECIFIC"}), 400
                
                # Verify departments exist
                for dept_id in dept_ids:
                    dept = Department.get_department_by_id(session, dept_id)
                    if not dept:
                        return jsonify({"message": f"Department with ID {dept_id} not found"}), 404

            # Validate employee IDs if targeting specific employees
            if data.get('target_audience') == 'CUSTOM':
                emp_ids = data.get('employee_ids', [])
                if not emp_ids:
                    return jsonify({"message": "employee_ids is required when target_audience is CUSTOM"}), 400
                
                # Verify employees exist
                for emp_id in emp_ids:
                    emp = Employee.get_employee_by_id(session, emp_id)
                    if not emp:
                        return jsonify({"message": f"Employee with ID {emp_id} not found"}), 404

            # Parse dates if provided
            if data.get('publish_date'):
                try:
                    data['publish_date'] = datetime.fromisoformat(data['publish_date'].replace('Z', '+00:00'))
                    app.logger.info(f"Parsed publish_date: {data['publish_date']}")
                except ValueError as e:
                    app.logger.error(f"Invalid publish_date format: {data.get('publish_date')} - {e}")
                    return jsonify({"message": "Invalid publish_date format. Use ISO format."}), 400

            if data.get('expiry_date'):
                try:
                    data['expiry_date'] = datetime.fromisoformat(data['expiry_date'].replace('Z', '+00:00'))
                    app.logger.info(f"Parsed expiry_date: {data['expiry_date']}")
                except ValueError as e:
                    app.logger.error(f"Invalid expiry_date format: {data.get('expiry_date')} - {e}")
                    return jsonify({"message": "Invalid expiry_date format. Use ISO format."}), 400

            # Set created_by
            data['created_by'] = user_id
            app.logger.info(f"Set created_by to: {user_id} (type: {type(user_id)})")

            # Remove company_id from data as it's not a field in the Announcement model
            # (company context is determined by which database we're using)
            if 'company_id' in data:
                del data['company_id']
                app.logger.info("Removed company_id from data (not an Announcement field)")

            # Create the announcement
            app.logger.info("About to create announcement with data:")
            app.logger.info(f"Data keys: {list(data.keys())}")

            announcement, error = Announcement.create_announcement(session, **data)
            if not announcement:
                app.logger.error(f"Failed to create announcement: {error}")
                return jsonify({"message": f"Failed to create announcement: {error}"}), 500

            app.logger.info(f"Successfully created announcement: {announcement.announcement_id}")
            session.commit()
            return jsons.dump(Msg.success().add("announcement", announcement.to_dict())), 200

    except Exception as e:
        import traceback
        app.logger.error(f"Error creating announcement: {e}")
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"message": f"Internal server error: {str(e)}"}), 500


@announcements_api.route('/api/announcements', methods=['GET'])
@token_required
@roles_required('hr', 'admin')
def get_announcements():
    """
    Get announcements with filtering options.
    
    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - is_published: Filter by published status (true/false)
    - is_active: Filter by active status (true/false)
    - announcement_type: Filter by announcement type
    - priority: Filter by priority level
    - created_by: Filter by creator user ID
    - department_id: Filter by department targeting
    - search: Search in title, content, and summary
    - start_date: Filter by creation date (ISO format)
    - end_date: Filter by creation date (ISO format)
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        offset = (page - 1) * limit

        # Build filters
        filters = {}
        
        # Boolean filters
        if request.args.get('is_published') is not None:
            filters['is_published'] = request.args.get('is_published').lower() == 'true'
        if request.args.get('is_active') is not None:
            filters['is_active'] = request.args.get('is_active').lower() == 'true'
            
        # String filters
        for param in ['announcement_type', 'priority', 'created_by', 'department_id', 'search']:
            if request.args.get(param):
                filters[param] = request.args.get(param)
                
        # Date filters
        if request.args.get('start_date'):
            try:
                filters['start_date'] = datetime.fromisoformat(request.args.get('start_date').replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"message": "Invalid start_date format. Use ISO format."}), 400
                
        if request.args.get('end_date'):
            try:
                filters['end_date'] = datetime.fromisoformat(request.args.get('end_date').replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"message": "Invalid end_date format. Use ISO format."}), 400

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            announcements = Announcement.get_announcements_by_filters(
                session, filters=filters, limit=limit, offset=offset
            )
            
            # Get total count for pagination
            total_announcements = Announcement.get_announcements_by_filters(session, filters=filters)
            total_count = len(total_announcements)
            
            return jsonify({
                "message": "Announcements retrieved successfully",
                "announcements": [ann.to_dict(include_analytics=True) for ann in announcements],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_count,
                    "pages": (total_count + limit - 1) // limit
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting announcements: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>', methods=['GET'])
@token_required
@roles_required('hr', 'admin')
def get_announcement(announcement_id):
    """
    Get a specific announcement by ID.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            return jsons.dump(Msg.success().add("announcement", announcement.to_dict(include_analytics=True))), 200

    except Exception as e:
        app.logger.error(f"Error getting announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def update_announcement(announcement_id):
    """
    Update an announcement.
    
    Request Body: Same as create_announcement, all fields optional.
    """
    try:
        data = request.get_json()

        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Remove company_id from data if present (not an Announcement field)
        if 'company_id' in data:
            del data['company_id']

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if user can edit this announcement
            if not announcement.can_be_edited_by(user_id):
                return jsonify({"message": "You can only edit announcements you created"}), 403

            # Validate department IDs if targeting specific departments
            if data.get('target_audience') == 'DEPARTMENT_SPECIFIC':
                dept_ids = data.get('department_ids', [])
                if dept_ids:
                    for dept_id in dept_ids:
                        dept = Department.get_department_by_id(session, dept_id)
                        if not dept:
                            return jsonify({"message": f"Department with ID {dept_id} not found"}), 404

            # Validate employee IDs if targeting specific employees
            if data.get('target_audience') == 'CUSTOM':
                emp_ids = data.get('employee_ids', [])
                if emp_ids:
                    for emp_id in emp_ids:
                        emp = Employee.get_employee_by_id(session, emp_id)
                        if not emp:
                            return jsonify({"message": f"Employee with ID {emp_id} not found"}), 404

            # Parse dates if provided
            if data.get('publish_date'):
                try:
                    data['publish_date'] = datetime.fromisoformat(data['publish_date'].replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({"message": "Invalid publish_date format. Use ISO format."}), 400

            if data.get('expiry_date'):
                try:
                    data['expiry_date'] = datetime.fromisoformat(data['expiry_date'].replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({"message": "Invalid expiry_date format. Use ISO format."}), 400

            # Set updated_by
            data['updated_by'] = user_id

            # Update the announcement
            updated_announcement, error = Announcement.update_announcement(session, announcement_id, **data)
            if not updated_announcement:
                return jsonify({"message": f"Failed to update announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("announcement", updated_announcement.to_dict())), 200

    except Exception as e:
        app.logger.error(f"Error updating announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>', methods=['DELETE'])
@token_required
@roles_required('hr', 'admin')
def delete_announcement(announcement_id):
    """
    Delete an announcement.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if user can delete this announcement
            if not announcement.can_be_edited_by(user_id):
                return jsonify({"message": "You can only delete announcements you created"}), 403

            # Delete the announcement
            success, error = Announcement.delete_announcement(session, announcement_id)
            if not success:
                return jsonify({"message": f"Failed to delete announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("message", "Announcement deleted successfully")), 200

    except Exception as e:
        app.logger.error(f"Error deleting announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/publish', methods=['POST'])
@token_required
@roles_required('hr', 'admin')
def publish_announcement(announcement_id):
    """
    Publish an announcement.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if user can publish this announcement
            if not announcement.can_be_edited_by(user_id):
                return jsonify({"message": "You can only publish announcements you created"}), 403

            # Publish the announcement
            success, error = announcement.publish(session, user_id)
            if not success:
                return jsonify({"message": f"Failed to publish announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("announcement", announcement.to_dict())), 200

    except Exception as e:
        app.logger.error(f"Error publishing announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/unpublish', methods=['POST'])
@token_required
@roles_required('hr', 'admin')
def unpublish_announcement(announcement_id):
    """
    Unpublish an announcement.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if user can unpublish this announcement
            if not announcement.can_be_edited_by(user_id):
                return jsonify({"message": "You can only unpublish announcements you created"}), 403

            # Unpublish the announcement
            success, error = announcement.unpublish(session, user_id)
            if not success:
                return jsonify({"message": f"Failed to unpublish announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("announcement", announcement.to_dict())), 200

    except Exception as e:
        app.logger.error(f"Error unpublishing announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/archive', methods=['POST'])
@token_required
@roles_required('hr', 'admin')
def archive_announcement(announcement_id):
    """
    Archive an announcement.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if user can archive this announcement
            if not announcement.can_be_edited_by(user_id):
                return jsonify({"message": "You can only archive announcements you created"}), 403

            # Archive the announcement
            success, error = announcement.archive(session, user_id)
            if not success:
                return jsonify({"message": f"Failed to archive announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("announcement", announcement.to_dict())), 200

    except Exception as e:
        app.logger.error(f"Error archiving announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/analytics', methods=['GET'])
@token_required
@roles_required('hr', 'admin')
def get_announcement_analytics(announcement_id):
    """
    Get analytics for a specific announcement.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            analytics = Announcement.get_announcement_analytics(session, announcement_id)
            if not analytics:
                return jsonify({"message": "Announcement not found or analytics unavailable"}), 404

            return jsonify({
                "message": "Analytics retrieved successfully",
                "analytics": analytics
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting announcement analytics: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/reads', methods=['GET'])
@token_required
@roles_required('hr', 'admin')
def get_announcement_reads(announcement_id):
    """
    Get read records for a specific announcement.

    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        offset = (page - 1) * limit

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if announcement exists
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Get read records
            reads = AnnouncementRead.get_reads_by_announcement(
                session, announcement_id, limit=limit, offset=offset
            )

            # Get total count for pagination
            all_reads = AnnouncementRead.get_reads_by_announcement(session, announcement_id)
            total_count = len(all_reads)

            # Get read statistics
            read_stats = AnnouncementRead.get_read_statistics(session, announcement_id)

            return jsonify({
                "message": "Read records retrieved successfully",
                "reads": [read.to_dict() for read in reads],
                "statistics": read_stats,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_count,
                    "pages": (total_count + limit - 1) // limit
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting announcement reads: {e}")
        return jsonify({"message": "Internal server error"}), 500


# ==================== Employee Announcement Endpoints ====================

@announcements_api.route('/api/announcements/employee', methods=['GET'])
@token_required
def get_employee_announcements():
    """
    Get announcements for the logged-in employee.

    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - unread_only: Show only unread announcements (true/false)
    - include_expired: Include expired announcements (true/false, default: false)
    - priority: Filter by priority level
    - announcement_type: Filter by announcement type
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        offset = (page - 1) * limit

        # Get filter parameters
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'
        include_expired = request.args.get('include_expired', 'false').lower() == 'true'
        priority = request.args.get('priority')
        announcement_type = request.args.get('announcement_type')

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get the employee record for the logged-in user
            company_user = CompanyUser.get_user_by_id(session, user_id)
            if not company_user or not company_user.employee_id:
                return jsonify({"message": "Employee record not found"}), 404

            employee = Employee.get_employee_by_id(session, company_user.employee_id)
            if not employee:
                return jsonify({"message": "Employee record not found"}), 404

            # Get announcements for this employee
            announcements = Announcement.get_announcements_for_employee(
                session, employee, include_expired=include_expired, limit=limit, offset=offset
            )

            # Apply additional filters
            if priority:
                announcements = [ann for ann in announcements if ann.priority == priority]
            if announcement_type:
                announcements = [ann for ann in announcements if ann.announcement_type == announcement_type]

            # Filter for unread only if requested
            if unread_only:
                read_announcement_ids = [
                    str(read.announcement_id) for read in
                    AnnouncementRead.get_reads_by_employee(session, employee.employee_id)
                ]
                announcements = [
                    ann for ann in announcements
                    if str(ann.announcement_id) not in read_announcement_ids
                ]

            # Get unread count
            unread_count = AnnouncementRead.get_unread_announcements_count(session, employee.employee_id)

            return jsonify({
                "message": "Announcements retrieved successfully",
                "announcements": [ann.to_dict(include_content=False) for ann in announcements],
                "unread_count": unread_count,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": len(announcements),
                    "pages": (len(announcements) + limit - 1) // limit if announcements else 0
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting employee announcements: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/read', methods=['GET'])
@token_required
def get_announcement_for_employee(announcement_id):
    """
    Get a specific announcement for the logged-in employee and mark it as read.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get the employee record for the logged-in user
            company_user = CompanyUser.get_user_by_id(session, user_id)
            if not company_user or not company_user.employee_id:
                return jsonify({"message": "Employee record not found"}), 404

            employee = Employee.get_employee_by_id(session, company_user.employee_id)
            if not employee:
                return jsonify({"message": "Employee record not found"}), 404

            # Get the announcement
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if employee can view this announcement
            if not announcement.is_visible_to_employee(employee):
                return jsonify({"message": "You don't have permission to view this announcement"}), 403

            # Increment view count
            announcement.increment_view_count(session)

            # Create or update read record
            read_record, error = AnnouncementRead.create_read_record(
                session, announcement_id, employee.employee_id,
                device_type=request.headers.get('X-Device-Type', 'WEB'),
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            return jsons.dump(Msg.success().add("announcement", announcement.to_dict())), 200

    except Exception as e:
        app.logger.error(f"Error getting announcement for employee: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/<announcement_id>/acknowledge', methods=['POST'])
@token_required
def acknowledge_announcement(announcement_id):
    """
    Acknowledge an announcement (if it requires acknowledgment).
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get the employee record for the logged-in user
            company_user = CompanyUser.get_user_by_id(session, user_id)
            if not company_user or not company_user.employee_id:
                return jsonify({"message": "Employee record not found"}), 404

            employee = Employee.get_employee_by_id(session, company_user.employee_id)
            if not employee:
                return jsonify({"message": "Employee record not found"}), 404

            # Get the announcement
            announcement = Announcement.get_announcement_by_id(session, announcement_id)
            if not announcement:
                return jsonify({"message": "Announcement not found"}), 404

            # Check if employee can view this announcement
            if not announcement.is_visible_to_employee(employee):
                return jsonify({"message": "You don't have permission to view this announcement"}), 403

            # Check if announcement requires acknowledgment
            if not announcement.requires_acknowledgment:
                return jsonify({"message": "This announcement does not require acknowledgment"}), 400

            # Get or create read record
            read_record = AnnouncementRead.get_read_by_announcement_and_employee(
                session, announcement_id, employee.employee_id
            )

            if not read_record:
                # Create read record first
                read_record, error = AnnouncementRead.create_read_record(
                    session, announcement_id, employee.employee_id,
                    device_type=request.headers.get('X-Device-Type', 'WEB'),
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
                if not read_record:
                    return jsonify({"message": f"Failed to create read record: {error}"}), 500

            # Acknowledge the announcement
            if read_record.is_acknowledged:
                return jsonify({"message": "Announcement already acknowledged"}), 400

            success, error = read_record.acknowledge(session)
            if not success:
                return jsonify({"message": f"Failed to acknowledge announcement: {error}"}), 500

            return jsons.dump(Msg.success().add("message", "Announcement acknowledged successfully")), 200

    except Exception as e:
        app.logger.error(f"Error acknowledging announcement: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/unread-count', methods=['GET'])
@token_required
def get_unread_announcements_count():
    """
    Get the count of unread announcements for the logged-in employee.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get the employee record for the logged-in user
            company_user = CompanyUser.get_user_by_id(session, user_id)
            if not company_user or not company_user.employee_id:
                return jsonify({"message": "Employee record not found"}), 404

            employee = Employee.get_employee_by_id(session, company_user.employee_id)
            if not employee:
                return jsonify({"message": "Employee record not found"}), 404

            # Get unread count
            unread_count = AnnouncementRead.get_unread_announcements_count(session, employee.employee_id)

            return jsonify({
                "message": "Unread count retrieved successfully",
                "unread_count": unread_count
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting unread announcements count: {e}")
        return jsonify({"message": "Internal server error"}), 500


# ==================== Utility Endpoints ====================

@announcements_api.route('/api/announcements/types', methods=['GET'])
@token_required
def get_announcement_types():
    """
    Get available announcement types and priorities.
    """
    try:
        announcement_types = [
            'GENERAL', 'POLICY', 'EVENT', 'EMERGENCY', 'TRAINING',
            'BENEFITS', 'COMPANY_NEWS', 'SYSTEM_MAINTENANCE', 'HOLIDAY'
        ]

        priorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT']

        target_audiences = ['ALL', 'DEPARTMENT_SPECIFIC', 'ROLE_SPECIFIC', 'CUSTOM']

        return jsonify({
            "message": "Announcement configuration retrieved successfully",
            "announcement_types": announcement_types,
            "priorities": priorities,
            "target_audiences": target_audiences
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting announcement types: {e}")
        return jsonify({"message": "Internal server error"}), 500


@announcements_api.route('/api/announcements/departments', methods=['GET'])
@token_required
@roles_required('hr', 'admin')
def get_departments_for_targeting():
    """
    Get departments available for announcement targeting.
    """
    try:
        # Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get company database name
        company = Company.query.filter_by(company_id=company_id).first()
        if not company:
            return jsonify({"message": "Company not found"}), 404

        database_name = company.database_name

        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            departments = Department.get_all_departments(session)

            return jsonify({
                "message": "Departments retrieved successfully",
                "departments": [dept.to_dict() for dept in departments]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting departments: {e}")
        return jsonify({"message": "Internal server error"}), 500

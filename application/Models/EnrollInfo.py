"""This module defines the EnrollInfo class, which represents a record in the enrollinfo table in the database."""
from application.database import db

class EnrollInfo(db.Model):
    __tablename__ = 'enrollinfo'

    id = db.Column(db.Integer, primary_key=True)
    enroll_id = db.Column(db.Integer)
    backupnum = db.Column(db.Integer)
    imagepath = db.Column(db.String(255))
    signatures = db.Column(db.Text)

    def __str__(self):
        return "EnrollInfo [id={}, enroll_id={}, backupnum={}, imagepath={}, signatures={}]".format(self.id,
                                                                                                    self.enroll_id,
                                                                                                    self.backupnum,
                                                                                                    self.imagepath,
                                                                                                    self.signatures)
    def to_dict(self):
        return {
            "id": self.id,
            "enroll_id": self.enroll_id,
            "backupnum": self.backupnum,
            "imagepath": self.imagepath,
            "signatures": self.signatures
        }
    @classmethod
    def selectbybackupnum(cls, session, enroll_id, backupnum):
        """Return an EnrollInfo record by its enroll_id and backupnum."""
        selected = session.query(cls).filter_by(enroll_id=enroll_id, backupnum=backupnum).first()
        return selected
    
    # 插入新的记录
    def insert_enroll_info(self,**info):
        info = EnrollInfo(**info)

        db.session.add(info)
        db.session.commit()

    def get_all_enroll_info(self):
        return EnrollInfo.query.all()
    def select_all(self):
        return EnrollInfo.query.all()
    # 根据主键查询记录
    def get_enroll_info_by_id(self,id):

        return db.session.query(EnrollInfo).get(id)

    # 根据enroll_id查询记录
    def get_enroll_info_by_enroll_id(self,enroll_id):

        return db.session.query(EnrollInfo).filter_by(enroll_id=enroll_id).first()

    def selectByBackupnum(self, enroll_id , backupnum):

        return db.session.query(EnrollInfo).filter_by(enroll_id=enroll_id, backupnum=backupnum).first()
    # 更新记录
    def update_enroll_info(self,id, enroll_id, backupnum, imagepath, signatures):

        info = db.session.query(EnrollInfo).get(id)
        if info:
            info.enroll_id = enroll_id
            info.backupnum = backupnum
            info.imagepath = imagepath
            info.signatures = signatures
            db.session.commit()

    def delete_enroll_info(self,id):

        info = db.session.query(EnrollInfo).get(id)
        if info:
            db.session.delete(info)
            db.session.commit()
    def delete_by_enroll_id(self,id):

        info = db.session.query(EnrollInfo).get(id)
        if info:
            db.session.delete(info)
            db.session.commit()

def insert_enroll_info(session, **info):
    info = EnrollInfo(**info)

    try:
        session.add(info)
        session.commit()
        return info
    except Exception as e:
        session.rollback()
        return None



def get_all_enroll_info(session):
    try:
        info = session.query(EnrollInfo).all()
        return info
    except Exception as e:
        return None

# 根据主键查询记录
def get_enroll_info_by_id(session, id):
    """Retrieve an EnrollInfo record by its primary key."""
    try:
        info = session.query(EnrollInfo).get(id)
        return info
    except Exception as e:
        return None


# 根据enroll_id查询记录
def get_enroll_info_by_enroll_id(session, enroll_id):
    """Retrieve an EnrollInfo record by its enroll_id."""
    try:
        info = session.query(EnrollInfo).filter_by(enroll_id=enroll_id).first()
        return info
    except Exception as e:
        return None


# 更新记录
def update_enroll_info(session, id, enroll_id, backupnum, imagepath, signatures):
    """Update an EnrollInfo record with the provided values."""
    info = session.query(EnrollInfo).get(id)
    if info:
        info.enroll_id = enroll_id
        info.backupnum = backupnum
        info.imagepath = imagepath
        info.signatures = signatures
        session.commit()

def update_enroll_info2(session, enroll_id, imagepath, signatures): #新加 2024年1月25日10:41:03
    """Update an EnrollInfo record with the provided values."""
    info = session.query(EnrollInfo).filter_by(enroll_id=enroll_id, backupnum=50)
    if info:
        for info in info:
            info.imagepath = imagepath
            info.signatures = signatures
            session.commit()

def delete_enroll_info(session, id):

    info = session.query(EnrollInfo).get(id)
    if info:
        session.delete(info)
        session.commit()
        message = f"Deleted enroll info with id {info}."
        return message
def selectByBackupnum( session, enroll_id , backupnum):
    """Retrieve an EnrollInfo record by its enroll_id and backupnum."""
    try:
        selected = session.query(EnrollInfo).filter_by(enroll_id=enroll_id, backupnum=backupnum).first()
        return selected
    except Exception as e:
        return None

def update_by_primary_key_with_blobs(session, enroll_info):
    """Update an EnrollInfo record with the provided values.
    args:
        session: the current database session
        enroll_info: an EnrollInfo object with the updated values
        returns:
        a message indicating the result of the update
    """
    info = session.query(EnrollInfo).get(enroll_info.id)
    if info:
        info.enroll_id = enroll_info.enroll_id
        info.backupnum = enroll_info.backupnum
        info.imagepath = enroll_info.imagepath
        info.signatures = enroll_info.signatures
        session.commit()
        message = f"Updated enroll info with id {info}."
        return message
    else:
        return None
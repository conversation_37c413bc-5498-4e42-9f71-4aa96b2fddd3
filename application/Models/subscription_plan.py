from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID, JSONB
from flask import current_app
from datetime import datetime
from decimal import Decimal

class SubscriptionPlan(db.Model):
    """Model representing subscription plans (stored in central database)."""
    __tablename__ = 'subscription_plans'

    plan_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    price_per_employee = db.Column(db.Numeric(10, 2), nullable=False)
    billing_cycle = db.Column(db.String(20), default='MONTHLY', nullable=False)  # MONTHLY, QUARTERLY, YEARLY
    max_employees = db.Column(db.Integer, nullable=True)  # NULL = unlimited
    features = db.Column(JSONB, nullable=False, default={})  # Feature flags and limits
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    sort_order = db.Column(db.Integer, default=0, nullable=False)  # For display ordering
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    subscriptions = db.relationship('CompanySubscription', backref='plan', lazy='dynamic')

    def __str__(self):
        """Return a string representation of the object."""
        return f"SubscriptionPlan [plan_id={self.plan_id}, name={self.name}, price_per_employee={self.price_per_employee}]"

    def to_dict(self, include_features=True):
        """Dictionary representation of the object."""
        data = {
            "plan_id": str(self.plan_id),
            "name": self.name,
            "description": self.description,
            "price_per_employee": float(self.price_per_employee),
            "billing_cycle": self.billing_cycle,
            "max_employees": self.max_employees,
            "is_active": self.is_active,
            "sort_order": self.sort_order,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
        
        if include_features:
            data["features"] = self.features
            
        return data

    @classmethod
    def create_plan(cls, **kwargs):
        """Create a new subscription plan."""
        try:
            plan = cls(**kwargs)
            db.session.add(plan)
            db.session.commit()
            current_app.logger.info(f"Created subscription plan: {plan.name}")
            return plan, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating subscription plan: {e}")
            return None, str(e)

    @classmethod
    def get_plan_by_id(cls, plan_id):
        """Get a subscription plan by ID."""
        try:
            if isinstance(plan_id, str):
                plan_id = uuid.UUID(plan_id)
            return cls.query.filter_by(plan_id=plan_id).first()
        except Exception as e:
            current_app.logger.error(f"Error getting plan by ID: {e}")
            return None

    @classmethod
    def get_active_plans(cls):
        """Get all active subscription plans."""
        try:
            return cls.query.filter_by(is_active=True).order_by(cls.sort_order, cls.price_per_employee).all()
        except Exception as e:
            current_app.logger.error(f"Error getting active plans: {e}")
            return []

    @classmethod
    def get_all_plans(cls):
        """Get all subscription plans."""
        try:
            return cls.query.order_by(cls.sort_order, cls.price_per_employee).all()
        except Exception as e:
            current_app.logger.error(f"Error getting all plans: {e}")
            return []

    @classmethod
    def update_plan(cls, plan_id, **kwargs):
        """Update a subscription plan."""
        try:
            if isinstance(plan_id, str):
                plan_id = uuid.UUID(plan_id)
            
            plan = cls.query.filter_by(plan_id=plan_id).first()
            if not plan:
                return None, "Plan not found"

            for key, value in kwargs.items():
                if hasattr(plan, key):
                    setattr(plan, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated subscription plan: {plan.name}")
            return plan, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating subscription plan: {e}")
            return None, str(e)

    @classmethod
    def delete_plan(cls, plan_id):
        """Soft delete a subscription plan (mark as inactive)."""
        try:
            if isinstance(plan_id, str):
                plan_id = uuid.UUID(plan_id)
            
            plan = cls.query.filter_by(plan_id=plan_id).first()
            if not plan:
                return False, "Plan not found"

            # Check if plan has active subscriptions
            active_subscriptions = plan.subscriptions.filter_by(status='ACTIVE').count()
            if active_subscriptions > 0:
                return False, f"Cannot delete plan with {active_subscriptions} active subscriptions"

            plan.is_active = False
            db.session.commit()
            current_app.logger.info(f"Deactivated subscription plan: {plan.name}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deleting subscription plan: {e}")
            return False, str(e)

    def calculate_price(self, employee_count, billing_cycle=None):
        """Calculate the total price for given employee count and billing cycle."""
        if billing_cycle is None:
            billing_cycle = self.billing_cycle
            
        base_price = float(self.price_per_employee) * employee_count
        
        # Apply billing cycle multipliers
        multipliers = {
            'MONTHLY': 1,
            'QUARTERLY': 3,
            'YEARLY': 12
        }
        
        return base_price * multipliers.get(billing_cycle, 1)

    def has_feature(self, feature_name):
        """Check if the plan includes a specific feature."""
        return self.features.get(feature_name, False)

    def get_feature_limit(self, feature_name):
        """Get the limit for a specific feature."""
        feature_config = self.features.get(feature_name)
        if isinstance(feature_config, dict):
            return feature_config.get('limit')
        return None

    @classmethod
    def seed_default_plans(cls):
        """Create default subscription plans."""
        default_plans = [
            {
                'name': 'Starter',
                'description': 'Perfect for small teams getting started',
                'price_per_employee': Decimal('5.00'),
                'billing_cycle': 'MONTHLY',
                'max_employees': 25,
                'sort_order': 1,
                'features': {
                    'employee_management': True,
                    'attendance_tracking': True,
                    'basic_reports': True,
                    'announcements': True,
                    'leave_management': True,
                    'payroll': False,
                    'advanced_analytics': False,
                    'api_access': False,
                    'custom_integrations': False
                }
            },
            {
                'name': 'Professional',
                'description': 'Advanced features for growing businesses',
                'price_per_employee': Decimal('12.00'),
                'billing_cycle': 'MONTHLY',
                'max_employees': 100,
                'sort_order': 2,
                'features': {
                    'employee_management': True,
                    'attendance_tracking': True,
                    'basic_reports': True,
                    'announcements': True,
                    'leave_management': True,
                    'payroll': True,
                    'advanced_analytics': True,
                    'performance_management': True,
                    'recruitment': True,
                    'api_access': True,
                    'custom_integrations': False
                }
            },
            {
                'name': 'Enterprise',
                'description': 'Complete solution for large organizations',
                'price_per_employee': Decimal('20.00'),
                'billing_cycle': 'MONTHLY',
                'max_employees': None,  # Unlimited
                'sort_order': 3,
                'features': {
                    'employee_management': True,
                    'attendance_tracking': True,
                    'basic_reports': True,
                    'announcements': True,
                    'leave_management': True,
                    'payroll': True,
                    'advanced_analytics': True,
                    'performance_management': True,
                    'recruitment': True,
                    'onboarding': True,
                    'api_access': True,
                    'custom_integrations': True,
                    'priority_support': True,
                    'custom_fields': True
                }
            }
        ]

        try:
            for plan_data in default_plans:
                existing_plan = cls.query.filter_by(name=plan_data['name']).first()
                if not existing_plan:
                    plan = cls(**plan_data)
                    db.session.add(plan)
                    current_app.logger.info(f"Created default plan: {plan_data['name']}")

            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error seeding default plans: {e}")
            return False, str(e)

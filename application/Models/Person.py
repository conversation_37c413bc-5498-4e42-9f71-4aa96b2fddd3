from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app
# class Person:
#     def __init__(self, id=None, name=None, roll_id=None):
#         self.id = id
#         self.name = name if name is not None else None
#         self.roll_id = roll_id
#
#     def __str__(self):
#         return f"Person [id={self.id}, name={self.name}, rollId={self.roll_id}]"
#

class Person(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), nullable=False)
    roll_id = db.Column(db.Integer, nullable=False)
    employee_id = db.Column(db.String(80),  unique=True, nullable=True)

    def __str__(self):
        return f"Person [id={self.id}, name={self.name}, rollId={self.roll_id}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        employee_id = self.employee_id if self.employee_id is not None else None
        return {
            "id": self.id,
            "name": self.name,
            "roll_id": self.roll_id,
            "employee_id": employee_id
        }
    
    @classmethod
    def get_person_given_id(cls,session, id):
        """Get a person given an id."""
        person = session.get(cls, id)
        return person 

    @classmethod
    def update_person(cls, session, id, employee_id):
        """Update a person's employee id."""
        person = session.get(cls, id)
        person.employee_id = employee_id
        session.commit()
        return person
            
    @classmethod
    def get_persons(cls, session):
        """Get all persons."""
        persons = session.query(cls).all()
        return persons

    # 插入数据
    def insert_person(self,session, **person):
        person = Person(**person)
        try:
            session.add(person)
            session.commit()
            current_app.logger.info(f"Inserted new person with name '{person.name}'.")
            return person.id
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Failed to insert new person: {e}")
            return None

    @classmethod
    def add_employee(cls,session, **kwargs):
        """Add an employee to the database."""
        try:
            employee = cls(**kwargs)
            session.add(employee)
            session.commit()
            # Return the object
            return employee
        except Exception as e:
            current_app.logger.error(f"Error adding employee: {e}")
            return None
        
    @classmethod
    def select_person_by_their_id(cls, session, id):
        """select Person by Id."""
        person = session.query(cls).get(id)
        return person

    # 查询数据
    def select_all(self):
        return Person.query.all()

    def select_person_by_id(self,session, id):
        person = session.query(Person).get(id)
        return person

    # 删除数据
    def delete_person_by_id(self,id):
        person = select_person_by_id(id)
        if person:
            db.session.delete(person)
            db.session.commit()


        # 更新数据
    def update_person_by_id(self,id, name=None, roll_id=None):
        person = select_person_by_id(id)
        if name is not None:
            person.name = name
        if roll_id is not None:
            person.roll_id = roll_id
        db.session.commit()
    def delete_by_primary_key(self,id):
        person = select_person_by_id(id)
        if person:
            db.session.delete(person)
            db.session.commit()
def insert_person(session, **person):
    person = Person(**person)
    try:
        session.add(person)
        session.commit()
        current_app.logger.info(f"Inserted new person with name '{person.name}'.")
        return person.id
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"Failed to insert new person: {e}")
        return None

def insert_person2(session, person):
    """Insert a new person into the database using the provided session."""
    try:
        session.add(person)
        session.commit()
        current_app.logger.info(f"Inserted new person with name '{person.name}'.")
        return person.id
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"Failed to insert new person: {e}")
        return None

# 查询数据
def select_all(session):
    try:
        person = session.get(Person).all()
        current_app.logger.info(f"Retrieved all persons: {person}")
        return person
    except Exception as e:
        current_app.logger.error(f"Failed to retrieve all persons: {e}")
        return None

def select_person_by_id(session, id):
    try:
        person = session.get(Person, id)
        current_app.logger.info(f"Retrieved person by id '{id}': {person}")
        return person
    except Exception as e:
        current_app.logger.error(f"Failed to retrieve person by id: {e}")
        return None
# 删除数据
def delete_person_by_id(session, id):
    person = select_person_by_id(session, id)
    session.delete(person)
    session.commit()

# 更新数据
def update_person_by_id(session, id, name=None, roll_id=None):
    person = select_person_by_id(session, id)
    if name is not None:
        person.name = name
    if roll_id is not None:
        person.roll_id = roll_id
    session.commit()
def update_by_primary_key(session, person):
    person = select_person_by_id(session, person.id)
    if person:
        person.name = person.name
        person.roll_id = person.roll_id
        session.commit()

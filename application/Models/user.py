from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import abort, flash, current_app
import hashlib
import secrets
from sqlalchemy.exc import SQLAlchemyError

# Import the association table from the associations module
from application.Models.associations import user_company_association


class User(db.Model):
    """Model representing a user."""
    #from application.Models.company import Company
    __tablename__ = 'users'

    user_id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()), nullable=False)
    username = db.Column(db.String(255), unique=True, nullable=False)
    first_name = db.Column(db.String(255), nullable=True)
    last_name = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False)
    phone_number = db.Column(db.String(20), unique=True, nullable=False)
    salt = db.Column(db.String(255), nullable=False)
    is_2fa_enabled = db.Column(db.Boolean, default=False)
    otp_key = db.Column(db.String(255), nullable=True)
    is_active = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Many-to-many relationship
    companies = db.relationship(
        'Company',
        secondary=user_company_association,
        back_populates='users'
    )

    #relationship with the refresh token
    refresh_tokens = db.relationship(
    'RefreshToken',
    backref='user',
    lazy=True,
    cascade='all, delete-orphan'
)


    def __repr__(self):
        """Return a string representation of the object."""
        return f"""
        user_id: {self.user_id},
        username: {self.username},
        role: {self.role},
        first_name: {self.first_name},
        last_name: {self.last_name},
        email: {self.email},
        phone_number: {self.phone_number},
        created_at: {self.created_at},
        companies: {[company.company_name for company in self.companies]}
        """

    def to_dict(self):
        """Convert user object to dictionary."""
        # Get the companies associated with this user through the many-to-many relationship
        return {
            "user_id": self.user_id,
            "username": self.username,
            "role": self.role,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "created_at": self.created_at.strftime("%d/%m/%Y %H:%M:%S"),
            "email": self.email,
            "phone_number": self.phone_number,
            # Include all associated companies' information
            "companies": [{"company_id": company.company_id, "company_name": company.company_name} for company in self.companies],
            "company_names": [company.company_name for company in self.companies]
        }

    @staticmethod
    def hash_password(password):
        """Hash the password."""
        # Generate a random salt
        salt = secrets.token_hex(16)
        print("salt", salt)

        # Concatenate salt and password
        salted_password = salt + password
        print("salted_password", salted_password)

        # Hash the password before registering in the database.
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        print("hashed_password", hashed_password)
        return [hashed_password, salt]

    @classmethod
    def register_user(
        cls, username, email, password, phone_number,
        first_name, last_name, role, is_active=True):
        """Register a user."""
        hashed_password, salt = cls.hash_password(password)
        new_user = User(
            username=username,
            email=email,
            role=role,
            password=hashed_password,
            phone_number=phone_number,
            first_name=first_name,
            last_name=last_name,
            salt=salt,
            is_active=is_active
        )
        try:
            db.session.add(new_user)
            db.session.commit()
            return True, new_user
        except Exception as e:
            db.session.rollback()
            print(f"Error registering user: {str(e)}")
            return False, None

    @classmethod
    def assign_user_a_company(cls, user_id, company_id):
        """
        Assign a user to a company.

        Args:
            user_id (str): The ID of the user to assign.
            company_id (str): The ID of the company to assign the user to.

        Returns:
            dict: A dictionary containing success status and additional information.
        """
        from flask import current_app
        # Import Company model here to avoid circular imports
        from application.Models.company import Company

        # Validate inputs
        if not user_id or not company_id:
            current_app.logger.error("User ID or Company ID is missing")
            return {
                "success": False,
                "message": "User ID and Company ID are required",
                "error_code": "MISSING_PARAMETERS"
            }

        # Get user and company objects
        try:
            user = db.session.get(User, user_id)
            if not user:
                current_app.logger.error(f"User with ID {user_id} not found")
                return {
                    "success": False,
                    "message": f"User with ID {user_id} not found",
                    "error_code": "USER_NOT_FOUND"
                }

            company = db.session.get(Company, company_id)
            if not company:
                current_app.logger.error(f"Company with ID {company_id} not found")
                return {
                    "success": False,
                    "message": f"Company with ID {company_id} not found",
                    "error_code": "COMPANY_NOT_FOUND"
                }

            # Check if user is already assigned to this company
            if company in user.companies:
                current_app.logger.info(f"User {user_id} is already assigned to company {company_id}")
                return {
                    "success": True,
                    "message": "User is already assigned to this company",
                    "user": user.to_dict(),
                    "company": company.company_name,
                    "error_code": None
                }

            # Assign user to company
            user.companies.append(company)
            db.session.commit()

            current_app.logger.info(f"User {user_id} successfully assigned to company {company_id}")
            return {
                "success": True,
                "message": "User successfully assigned to company",
                "user": user.to_dict(),
                "company": company.company_name,
                "error_code": None
            }

        except SQLAlchemyError as e:
            db.session.rollback()
            error_message = str(e)
            current_app.logger.error(f"Database error assigning user to company: {error_message}")
            return {
                "success": False,
                "message": "Database error occurred while assigning user to company",
                "error_details": error_message,
                "error_code": "DATABASE_ERROR"
            }
        except Exception as e:
            db.session.rollback()
            error_message = str(e)
            current_app.logger.error(f"Unexpected error assigning user to company: {error_message}")
            return {
                "success": False,
                "message": "An unexpected error occurred",
                "error_details": error_message,
                "error_code": "UNEXPECTED_ERROR"
            }

    @classmethod
    def login_user(cls, username, password):
        """Login a user."""
        user =db.session.query(User).filter_by(username=username).first()
        if not user:
            return False
        salted_password = user.salt + password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        if hashed_password == user.password:
            return True
        return False

    @classmethod
    def check_old_password(cls, user_id, old_password):
        """Check the old password of a user."""
        user = db.session.query(User).filter_by(user_id=user_id).first()
        if not user:
            return False
        salted_password = user.salt + old_password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        if hashed_password == user.password:
            return True
        return False


    @classmethod
    def update_user(cls, user_id, username, email, role, password, phone_number):
        """Update a user."""
        user = db.session.get(User, user_id)
        user.username = username
        user.email = email
        user.role = role
        if password:
            user.password = password
        user.phone_number = phone_number

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user: {str(e)}")
            return False

    @classmethod
    def update_profile(cls, user_id, username, email, phone, first_name, last_name):
        """Update a user profile."""
        user = db.session.get(User, user_id)
        user.username = username
        user.email = email
        user.phone_number = phone
        user.first_name = first_name
        user.last_name = last_name

        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Error updating user profile: {str(e)}")
            return False

    @classmethod
    def get_users(cls):
        """Get all users from the database."""
        try:
            users = db.session.query(User).all()
            return [user.to_dict() for user in users]
        except Exception as e:
            print("Error getting users: ", e)
            return []

    @classmethod
    def get_user_by_id(cls, user_id, abort_on_not_found=True):
        """Get user by ID."""
        user = db.session.get(User, user_id)
        if not user and abort_on_not_found:
            abort(404, description="User not found")

        # convert user to a dictionary
        converted_user = user.to_dict()
        return converted_user

    @staticmethod
    def get_user_by_email( email):
        """Get user by email."""
        user = db.session.query(User).filter_by(email=email).first()
        if not user:
            abort(404, description="User not found")
        return user

    @staticmethod
    def get_user_by_username(username):
        """Get user by username."""
        user = db.session.query(User).filter_by(username=username).first()
        if not user:
            abort(404, description="User not found")
        return user

    @classmethod
    def delete_user(cls, user_id):
        """Delete a user."""
        user = db.session.get(User, user_id)
        if not user:
            abort(404, description="User not found")
        db.session.delete(user)
        db.session.commit()
        return True

    @classmethod
    def update_otp_key(cls, otp_key, email):
        """Update the OTP key for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.otp_key = otp_key
        user.is_2fa_enabled = True
        db.session.commit()
        return True

    @classmethod
    def update_2fa_status(cls, email, status):
        """Update the 2FA status for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.is_2fa_enabled = status
        db.session.commit()
        return True

    @classmethod
    def reset_password(cls, email, password):
        """Reset the password for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        hashed_password, salt = cls.hash_password(password)
        user.password = hashed_password
        user.salt = salt
        db.session.commit()
        return True

    @classmethod
    def update_is_active(cls, email):
        """Update the is_active status for a user."""
        user = db.session.query(User).filter_by(email=email).first()
        user.is_active = True
        db.session.commit()
        print("User is active")
        return True

    @classmethod
    def check_user_existence(cls, identifier, value):
        """Check if a user exists.
        Description: This method checks if a user exists in the database.
        Args:
            identifier (str): The identifier to use for checking the user.
            value (str): The value of the identifier.
            Returns:
                bool: A boolean indicating whether the user exists.
        """
        user = db.session.query(User).filter_by(**{identifier: value}).first()
        if user:
            message = f"Registration failed: User with {identifier} {value} already exists."
            flash(message, "danger")
            current_app.logger.error(message)
            return True
        return False

    @classmethod
    def register_company_user(
        cls, username, email, role, password, phone_number,
        first_name, last_name, company_id):
        """Register a user and associate them with a specific company."""
        try:
            # Import Company model here to avoid circular imports
            from application.Models.company import Company

            # Hash the password
            hashed_password, salt = cls.hash_password(password)

            # Validate the company ID
            company = Company.query.get(company_id)
            if not company:
                return {"success": False, "message": f"Company with ID {company_id} does not exist."}

            # Check if a user with the same email or username already exists
            existing_user = User.query.filter(
                (User.email == email) | (User.username == username)
            ).first()
            if existing_user:
                return {"success": False, "message": "A user with this email or username already exists."}

            # Create a new user
            new_user = User(
                username=username,
                email=email,
                role=role,
                password=hashed_password,
                phone_number=phone_number,
                first_name=first_name,
                last_name=last_name,
                salt=salt,
                is_active=False
            )
            db.session.add(new_user)
            db.session.flush()  # Ensure new_user gets a user_id

            if company in new_user.companies:
                return {"success": False, "message": "User already assigned to the company."}

            # Associate the user with the company
            if company not in new_user.companies:
                new_user.companies.append(company)

            # Commit the transaction
            db.session.commit()
            return {"success": True, "message": "User successfully registered and assigned to the company."}
        except ValueError as ve:
            current_app.logger.error(f"Error registering user: {str(ve)}")
            db.session.rollback()
            return {"success": False, "message": str(ve)}
        except SQLAlchemyError as e:
            current_app.logger.error(f"Error registering user: {str(e)}")
            db.session.rollback()
            return {"success": False, "message": "A database error occurred."}
        except Exception as e:
            current_app.logger.error(f"Error registering user: {str(e)}")
            db.session.rollback()
            return {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
    @classmethod
    def update_company_user(
        cls, id, username, email, role, phone, company, first_name, last_name):
        """Update a company user."""
        # Import Company model here to avoid circular imports
        from application.Models.company import Company

        user = cls.get_user_by_id(id)
        user.username = username
        user.email = email
        user.role = role
        user.phone_number = phone
        user.first_name = first_name
        user.last_name = last_name
        user.companies = [Company.query.get(company)]
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating company user: {str(e)}")
            return False

    @classmethod
    def get_company_users(cls, company_id):
        """Get all users associated with a specific company."""
        from application.Models.associations import user_company_association

        # The users should be found in the user_company_association table
        users = db.session.query(User).join(user_company_association).filter(user_company_association.c.company_id == company_id).all()
        return [user.to_dict() for user in users]
"""
This module contains helper methods for various tasks.
"""
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from threading import Thread
from dotenv import load_dotenv
load_dotenv()

class HelperMethods:
    """
    A class containing helper methods for various tasks.
    """
    @staticmethod
    def parse_date(date_str):
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return None
        
    @staticmethod
    def send_email(receiver_email, subject, html_message):
        """Sends an email using SMTP with HTML content."""
        smtp_server = os.getenv("smtp_server")
        smtp_port = int(os.getenv("smtp_port", 465))
        sender_email = os.getenv("sender_email")
        password = os.getenv("email_password")
        def email_task():
            try:
                # Connect to SMTP server
                server = smtplib.SMTP_SSL(smtp_server, smtp_port)
                server.login(sender_email, password)

                # Create a multipart message
                message = MIMEMultipart()
                message["From"] = sender_email
                message["To"] = receiver_email
                message["Subject"] = subject

                # Attach HTML message
                message.attach(MIMEText(html_message, "html"))

                # Send email
                server.sendmail(sender_email, receiver_email, message.as_string())
                server.quit()
                print("Email sent successfully!")
            except Exception as e:
                print(f"Failed to send email. Error: {e}")

        # Start email sending in a new thread
        thread = Thread(target=email_task)
        thread.start()
        return thread

if __name__ == '__main__':
    sender_email = os.getenv('sender_email')
    password = os.getenv("email_password")
    smtp_server = os.getenv("smtp_server")
    receiver_email = "<EMAIL>"
    smtp_port = 465
    subject = "Testing"
    print(f"sender_email: {sender_email}")
    print(f"password: {password}")
    print(f"smtp_server: {smtp_server}")
    print(f"receiver_email: {receiver_email}")
    print(f"smtp port {smtp_port}")
    
    html_message = f"""
    <html>
    <body>
    <h1>This is a test email</h1>
    <p>Hello, this is a test email sent using Python.</p>
    <p>Here is your temporary password: <strong>{password}</strong></p>
    </body>
    </html>
    """
    try:
        thread = HelperMethods.send_email(receiver_email, subject, html_message)
        thread.join()  # Wait for the thread to complete before exiting
    except Exception as e:
        print(f"Failed to send email. Error: {e}")
    print("Email thread finished.")
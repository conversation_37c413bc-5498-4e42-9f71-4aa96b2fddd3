from flask import Blueprint, request, current_app, render_template
from flask_restx import Api, Resource, fields
import json
from functools import wraps

# Create a blueprint for the API documentation
api_doc = Blueprint('api_doc', __name__)

# Initialize the API with the blueprint
api = Api(api_doc,
          version='1.0',
          title='Attendance API',
          description='''
A comprehensive API for attendance management and employee tracking.

## Overview
This API provides endpoints for managing attendance, employees, companies, and users.
It supports authentication, role-based access control, and multi-tenant architecture.

## Authentication
To use the API, you need to authenticate using JWT tokens:

1. Use the `/users/login` endpoint to obtain an access token and refresh token
2. Include the access token in the Authorization header for authenticated requests:
   ```
   Authorization: Bearer YOUR_ACCESS_TOKEN
   ```
3. When the access token expires, use the refresh token to get a new one

## Rate Limiting
API requests are limited to 100 requests per minute per IP address.

## Error Handling
The API returns standard HTTP status codes and JSON error responses:
```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information"
}
```
''',
          doc='/docs',
          contact='<EMAIL>',
          license='MIT',
          license_url='https://opensource.org/licenses/MIT',
          terms_url='https://example.com/terms/',
          default_mediatype='application/json',
          ordered=True,
          template='swagger-ui.html')

# Configure security definitions
authorizations = {
    'Bearer Auth': {
        'type': 'apiKey',
        'in': 'header',
        'name': 'Authorization',
        'description': '''
## Authentication
To authenticate with the API, follow these steps:

### Step 1: Obtain Access Token
Make a POST request to `/users/login` with your credentials:
```json
{
  "username": "<EMAIL>",
  "password": "your_password"
}
```

### Step 2: Use the Access Token
Include the access token in the Authorization header for all authenticated requests:
```
Authorization: Bearer your_access_token_here
```

### Step 3: Token Expiration
Access tokens expire after 24 hours. When this happens, use your refresh token to get a new access token.

### Step 4: Logout
To logout and invalidate your tokens, make a POST request to `/users/logout` with your access token in the Authorization header.
'''
    },
}
api.authorizations = authorizations

# Define namespaces for different resource groups with proper paths
user_ns = api.namespace('users',
                       description='User management including registration, authentication, and profile management',
                       path='/users')

company_ns = api.namespace('companies',
                          description='Company operations for managing organizations and their settings',
                          path='/companies')

employee_ns = api.namespace('employees',
                           description='Employee management for handling employee records, departments, and positions',
                           path='/employees')

department_ns = api.namespace('departments',
                             description='Department operations for organizing employees within companies',
                             path='/departments')

device_ns = api.namespace('devices',
                         description='Device management for biometric and attendance tracking devices',
                         path='/devices')

attendance_ns = api.namespace('attendance',
                             description='Attendance tracking and reporting for employee time records',
                             path='/attendance')

sdk_ns = api.namespace('sdk',
                      description='SDK and code sample generation for API integration',
                      path='/sdk')

payroll_ns = api.namespace('payroll',
                          description='Payroll management for calculating salaries, taxes, and deductions',
                          path='/payroll')

# ==================== COMMON MODELS ====================
error_model = api.model('ErrorResponse', {
    'success': fields.Boolean(required=True, description='Success status'),
    'message': fields.String(required=True, description='Error message'),
    'error': fields.String(description='Detailed error information')
})

success_model = api.model('SuccessResponse', {
    'success': fields.Boolean(required=True, description='Success status'),
    'message': fields.String(description='Success message')
})

# ==================== COMPANY MODELS ====================
company_model = api.model('Company', {
    'company_id': fields.String(required=True, description='Company ID'),
    'company_name': fields.String(required=True, description='Company name'),
    'database_name': fields.String(required=True, description='Database name'),
    'company_tin': fields.String(description='Company Tax Identification Number'),
    'phone_number': fields.String(description='Company phone number'),
    'created_at': fields.DateTime(description='Creation timestamp')
})

company_device_model = api.model('CompanyDevice', {
    'id': fields.Integer(description='Device ID'),
    'company_id': fields.String(required=True, description='Company ID'),
    'device_sn': fields.String(required=True, description='Device serial number'),
    'created_at': fields.DateTime(description='Creation timestamp')
})

add_company_input = api.model('AddCompanyInput', {
    'company_name': fields.String(required=True, description='Company name'),
    'database_name': fields.String(description='Database name (generated if not provided)'),
    'company_id': fields.String(description='Company ID (generated if not provided)'),
    'company_tin': fields.String(required=True, description='Company Tax Identification Number'),
    'phone_number': fields.String(description='Company phone number')
})

add_company_device_input = api.model('AddCompanyDeviceInput', {
    'company_id': fields.String(required=True, description='Company ID'),
    'device_sn': fields.String(required=True, description='Device serial number')
})

# ==================== USER MODELS ====================
user_model = api.model('User', {
    'user_id': fields.String(required=True, description='User ID'),
    'username': fields.String(required=True, description='Username'),
    'email': fields.String(required=True, description='Email address'),
    'role': fields.String(required=True, description='User role (admin, hr, employee)'),
    'first_name': fields.String(description='First name'),
    'last_name': fields.String(required=True, description='Last name'),
    'full_name': fields.String(description='Full name'),
    'phone_number': fields.String(required=True, description='Phone number'),
    'created_at': fields.String(description='Creation timestamp'),
    'companies': fields.List(fields.Nested(api.model('UserCompany', {
        'company_id': fields.String(description='Company ID'),
        'company_name': fields.String(description='Company name')
    })), description='Associated companies'),
    'company_names': fields.List(fields.String, description='List of company names')
})

login_input_model = api.model('LoginInput', {
    'username': fields.String(required=True, description='Username'),
    'password': fields.String(required=True, description='Password')
})

login_response_model = api.model('LoginResponse', {
    'authenticated': fields.Boolean(required=True, description='Authentication status'),
    'access_token': fields.String(description='JWT access token'),
    'refresh_token': fields.String(description='JWT refresh token'),
    'user': fields.Nested(api.model('UserBasic', {
        'id': fields.String(description='User ID'),
        'role': fields.String(description='User role'),
        'name': fields.String(description='User full name')
    })),
    'companies': fields.List(fields.Nested(company_model), description='Associated companies')
})

register_input_model = api.model('RegisterInput', {
    'first_name': fields.String(required=True, description='First name'),
    'last_name': fields.String(required=True, description='Last name'),
    'email': fields.String(required=True, description='Email address'),
    'confirm_email': fields.String(required=True, description='Confirm email address'),
    'password': fields.String(required=True, description='Password'),
    'confirm_password': fields.String(required=True, description='Confirm password'),
    'phone_number': fields.String(required=True, description='Phone number'),
    'role': fields.String(required=True, description='User role (admin, hr, employee)')
})

register_response_model = api.model('RegisterResponse', {
    'registered': fields.Boolean(required=True, description='Registration status'),
    'user': fields.Nested(api.model('RegisteredUser', {
        'id': fields.String(description='User ID'),
        'username': fields.String(description='Username'),
        'name': fields.String(description='User full name'),
        'email': fields.String(description='Email address'),
        'role': fields.String(description='User role')
    }))
})

# ==================== EMPLOYEE MODELS ====================
employee_model = api.model('Employee', {
    'employee_id': fields.String(required=True, description='Employee ID'),
    'first_name': fields.String(required=True, description='First name'),
    'last_name': fields.String(required=True, description='Last name'),
    'id_number': fields.String(description='National ID number'),
    'email': fields.String(description='Email address'),
    'phone_number': fields.String(description='Phone number'),
    'department_id': fields.String(description='Department ID'),
    'position': fields.String(description='Job position'),
    'hire_date': fields.Date(description='Hire date'),
    'status': fields.String(description='Employee status (active, inactive)')
})

create_employee_input = api.model('CreateEmployeeInput', {
    'company_id': fields.String(required=True, description='Company ID'),
    'employee_id': fields.String(description='Employee ID (generated if not provided)'),
    'first_name': fields.String(required=True, description='First name'),
    'last_name': fields.String(required=True, description='Last name'),
    'id_number': fields.String(description='National ID number'),
    'email': fields.String(description='Email address'),
    'phone_number': fields.String(description='Phone number'),
    'department': fields.String(description='Department ID'),
    'position': fields.String(description='Job position'),
    'hire_date': fields.String(description='Hire date (YYYY-MM-DD)'),
    'status': fields.String(description='Employee status (active, inactive)')
})

# ==================== DEPARTMENT MODELS ====================
department_model = api.model('Department', {
    'department_id': fields.String(required=True, description='Department ID'),
    'name': fields.String(required=True, description='Department name'),
    'description': fields.String(description='Department description'),
    'manager_id': fields.String(description='Manager employee ID'),
    'created_at': fields.DateTime(description='Creation timestamp')
})

create_department_input = api.model('CreateDepartmentInput', {
    'company_id': fields.String(required=True, description='Company ID'),
    'name': fields.String(required=True, description='Department name'),
    'description': fields.String(description='Department description'),
    'manager_id': fields.String(description='Manager employee ID')
})

# ==================== ATTENDANCE MODELS ====================
attendance_model = api.model('Attendance', {
    'attendance_id': fields.String(required=True, description='Attendance ID'),
    'employee_id': fields.String(required=True, description='Employee ID'),
    'check_in': fields.DateTime(description='Check-in time'),
    'check_out': fields.DateTime(description='Check-out time'),
    'date': fields.Date(required=True, description='Attendance date'),
    'status': fields.String(description='Attendance status (present, absent, late)'),
    'source': fields.String(description='Attendance source (manual, biometric, facial)')
})

# ==================== DEVICE MODELS ====================
device_model = api.model('Device', {
    'id': fields.Integer(description='Device ID'),
    'serial_num': fields.String(required=True, description='Device serial number'),
    'status': fields.Integer(description='Device status')
})

# ==================== USER API ENDPOINTS ====================
@user_ns.route('/login')
class UserLogin(Resource):
    @user_ns.doc('login_user',
                examples={
                    'Valid login': {
                        'summary': 'Valid login example',
                        'value': {
                            'username': '<EMAIL>',
                            'password': 'password123'
                        }
                    }
                })
    @user_ns.expect(login_input_model)
    @user_ns.response(200, 'Success', login_response_model,
                     examples={
                         'Success': {
                             'value': {
                                 'authenticated': True,
                                 'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                                 'refresh_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                                 'user': {
                                     'id': '123e4567-e89b-12d3-a456-************',
                                     'role': 'admin',
                                     'name': 'John Doe'
                                 },
                                 'companies': [
                                     {
                                         'company_id': '123e4567-e89b-12d3-a456-426614174001',
                                         'company_name': 'ACME Inc.',
                                         'database_name': 'acme_db'
                                     }
                                 ]
                             }
                         }
                     })
    @user_ns.response(401, 'Invalid credentials', error_model,
                     examples={
                         'Invalid credentials': {
                             'value': {
                                 'authenticated': False,
                                 'message': 'Invalid credentials'
                             }
                         }
                     })
    @user_ns.response(404, 'User not found', error_model,
                     examples={
                         'User not found': {
                             'value': {
                                 'authenticated': False,
                                 'message': 'User not found'
                             }
                         }
                     })
    @user_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Login a user

        This endpoint authenticates a user and returns tokens and user information.
        The access token should be used in the Authorization header for authenticated requests.
        The refresh token can be used to obtain a new access token when the current one expires.
        """
        pass  # This is just for documentation, the actual implementation is in your blueprint

@user_ns.route('/register_user')
class UserRegister(Resource):
    @user_ns.doc('register_user',
                examples={
                    'Valid registration': {
                        'summary': 'Valid registration example',
                        'value': {
                            'first_name': 'John',
                            'last_name': 'Doe',
                            'email': '<EMAIL>',
                            'confirm_email': '<EMAIL>',
                            'password': 'password123',
                            'confirm_password': 'password123',
                            'phone_number': '1234567890',
                            'role': 'employee'
                        }
                    }
                })
    @user_ns.expect(register_input_model)
    @user_ns.response(200, 'User created', register_response_model,
                     examples={
                         'Success': {
                             'value': {
                                 'registered': True,
                                 'user': {
                                     'id': '123e4567-e89b-12d3-a456-************',
                                     'username': '<EMAIL>',
                                     'name': 'John Doe',
                                     'email': '<EMAIL>',
                                     'role': 'employee'
                                 }
                             }
                         }
                     })
    @user_ns.response(400, 'Missing required fields', error_model,
                     examples={
                         'Missing fields': {
                             'value': {
                                 'registered': False,
                                 'message': 'Missing required fields.',
                                 'missing_fields': ['first_name', 'last_name']
                             }
                         }
                     })
    @user_ns.response(409, 'Validation failed', error_model,
                     examples={
                         'Validation failed': {
                             'value': {
                                 'registered': False,
                                 'message': 'Validation failed',
                                 'errors': [
                                     {'field': 'email', 'error': 'Email already exists'},
                                     {'field': 'confirm_password', 'error': 'Passwords do not match'}
                                 ]
                             }
                         }
                     })
    @user_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Register a new user

        This endpoint registers a new user in the system. All required fields must be provided,
        and validation checks are performed to ensure data integrity. Upon successful registration,
        the user will be able to login with their email as username and the provided password.
        """
        pass  # This is just for documentation, the actual implementation is in your blueprint

@user_ns.route('/get_user/<string:user_id>')
@user_ns.param('user_id', 'The user identifier (UUID format)')
class UserResource(Resource):
    @user_ns.doc('get_user', security='Bearer Auth',
                examples={
                    'Example request': {
                        'summary': 'Get user by ID',
                        'description': 'Request to get user details by ID',
                        'value': None  # GET request has no body
                    }
                })
    @user_ns.response(200, 'Success', api.model('GetUserResponse', {
        'success': fields.Boolean(required=True, description='Success status'),
        'user': fields.Nested(user_model)
    }),
                     examples={
                         'Success': {
                             'value': {
                                 'success': True,
                                 'user': {
                                     'user_id': '123e4567-e89b-12d3-a456-************',
                                     'username': '<EMAIL>',
                                     'email': '<EMAIL>',
                                     'role': 'admin',
                                     'first_name': 'John',
                                     'last_name': 'Doe',
                                     'full_name': 'John Doe',
                                     'phone_number': '1234567890',
                                     'created_at': '2023-01-01T12:00:00Z',
                                     'companies': [
                                         {
                                             'company_id': '123e4567-e89b-12d3-a456-426614174001',
                                             'company_name': 'ACME Inc.'
                                         }
                                     ],
                                     'company_names': ['ACME Inc.']
                                 }
                             }
                         }
                     })
    @user_ns.response(404, 'User not found', error_model,
                     examples={
                         'User not found': {
                             'value': {
                                 'success': False,
                                 'message': 'User not found'
                             }
                         }
                     })
    @user_ns.response(401, 'Unauthorized', error_model,
                     examples={
                         'Unauthorized': {
                             'value': {
                                 'success': False,
                                 'message': 'Authentication token is missing or invalid'
                             }
                         }
                     })
    @user_ns.response(500, 'Server error', error_model)
    def get(self, user_id):
        """
        Get a user by ID

        This endpoint returns detailed user information including associated companies.
        Requires authentication with a valid JWT token in the Authorization header.

        The user_id parameter should be a valid UUID of the user to retrieve.
        Only administrators can retrieve information about any user. Regular users
        can only retrieve their own information.
        """
        pass  # This is just for documentation, the actual implementation is in your blueprint

@user_ns.route('/logout')
class UserLogout(Resource):
    @user_ns.doc('logout_user', security='Bearer Auth',
                examples={
                    'Example request': {
                        'summary': 'Logout request',
                        'description': 'Request to logout and invalidate tokens',
                        'value': {}  # Empty body for logout
                    }
                })
    @user_ns.response(200, 'Logout successful', api.model('LogoutResponse', {
        'message': fields.String(required=True, description='Success message')
    }),
                     examples={
                         'Success': {
                             'value': {
                                 'message': 'Logout successful'
                             }
                         }
                     })
    @user_ns.response(400, 'Logout failed', error_model,
                     examples={
                         'Failed': {
                             'value': {
                                 'message': 'Logout failed'
                             }
                         }
                     })
    @user_ns.response(401, 'Unauthorized', error_model,
                     examples={
                         'Unauthorized': {
                             'value': {
                                 'success': False,
                                 'message': 'Authentication token is missing or invalid'
                             }
                         }
                     })
    def post(self):
        """
        Logout a user

        This endpoint revokes the user's refresh token, effectively logging them out.
        Requires authentication with a valid JWT token in the Authorization header.

        After successful logout, the token will no longer be valid for authentication,
        and the user will need to login again to obtain new tokens.
        """
        pass  # This is just for documentation, the actual implementation is in your blueprint

# ==================== COMPANY API ENDPOINTS ====================
@company_ns.route('/add_company')
class AddCompany(Resource):
    @company_ns.doc('add_company', security='Bearer Auth')
    @company_ns.expect(add_company_input)
    @company_ns.response(200, 'Company added successfully')
    @company_ns.response(400, 'Bad request', error_model)
    @company_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Add a new company

        This endpoint adds a new company to the system.
        Requires HR role.
        """
        pass

@company_ns.route('/get_companies')
class GetCompanies(Resource):
    @company_ns.doc('get_companies', security='Bearer Auth')
    @company_ns.response(200, 'Success', api.model('GetCompaniesResponse', {
        'companies': fields.List(fields.Nested(company_model))
    }))
    def get(self):
        """
        Get all companies

        This endpoint returns all companies in the system.
        Requires authentication.
        """
        pass

@company_ns.route('/add_company_device')
class AddCompanyDevice(Resource):
    @company_ns.doc('add_company_device', security='Bearer Auth')
    @company_ns.expect(add_company_device_input)
    @company_ns.response(200, 'Device added successfully')
    @company_ns.response(400, 'Bad request', error_model)
    @company_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Add a device to a company

        This endpoint adds a device to a company.
        Requires admin role.
        """
        pass

@company_ns.route('/get_company_devices')
@company_ns.param('company_id', 'The company identifier')
class GetCompanyDevices(Resource):
    @company_ns.doc('get_company_devices', security='Bearer Auth')
    @company_ns.response(200, 'Success', api.model('GetCompanyDevicesResponse', {
        'devices': fields.List(fields.String(description='Device serial number'))
    }))
    def get(self):
        """
        Get all devices for a company

        This endpoint returns all devices for a specific company.
        Requires authentication.
        """
        pass

# ==================== EMPLOYEE API ENDPOINTS ====================
@employee_ns.route('/api/employees')
@employee_ns.param('company_id', 'The company identifier')
class EmployeeList(Resource):
    @employee_ns.doc('get_employees', security='Bearer Auth')
    @employee_ns.response(200, 'Success')
    @employee_ns.response(400, 'Bad request', error_model)
    @employee_ns.response(404, 'Company not found', error_model)
    def get(self):
        """
        Get all employees for a company

        This endpoint returns all employees for a specific company.
        Requires authentication.
        """
        pass

    @employee_ns.doc('create_employee', security='Bearer Auth')
    @employee_ns.expect(create_employee_input)
    @employee_ns.response(200, 'Employee created')
    @employee_ns.response(400, 'Bad request', error_model)
    @employee_ns.response(404, 'Company not found', error_model)
    @employee_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Create a new employee

        This endpoint creates a new employee for a specific company.
        Requires admin or HR role.
        """
        pass

@employee_ns.route('/api/employees/<string:employee_id>')
@employee_ns.param('employee_id', 'The employee identifier')
@employee_ns.param('company_id', 'The company identifier')
class EmployeeResource(Resource):
    @employee_ns.doc('get_employee', security='Bearer Auth')
    @employee_ns.response(200, 'Success')
    @employee_ns.response(400, 'Bad request', error_model)
    @employee_ns.response(404, 'Employee or company not found', error_model)
    def get(self, employee_id):
        """
        Get an employee by ID

        This endpoint returns an employee by ID for a specific company.
        Requires authentication.
        """
        pass

    @employee_ns.doc('update_employee', security='Bearer Auth')
    @employee_ns.expect(create_employee_input)
    @employee_ns.response(200, 'Employee updated')
    @employee_ns.response(400, 'Bad request', error_model)
    @employee_ns.response(404, 'Employee or company not found', error_model)
    @employee_ns.response(500, 'Server error', error_model)
    def put(self, employee_id):
        """
        Update an employee

        This endpoint updates an employee for a specific company.
        Requires admin or HR role.
        """
        pass

    @employee_ns.doc('delete_employee', security='Bearer Auth')
    @employee_ns.response(200, 'Employee deleted')
    @employee_ns.response(400, 'Bad request', error_model)
    @employee_ns.response(404, 'Employee or company not found', error_model)
    @employee_ns.response(500, 'Server error', error_model)
    def delete(self, employee_id):
        """
        Delete an employee

        This endpoint deletes an employee for a specific company.
        Requires admin or HR role.
        """
        pass

# ==================== DEPARTMENT API ENDPOINTS ====================
@department_ns.route('/api/departments')
@department_ns.param('company_id', 'The company identifier')
class DepartmentList(Resource):
    @department_ns.doc('get_departments', security='Bearer Auth')
    @department_ns.response(200, 'Success')
    @department_ns.response(400, 'Bad request', error_model)
    @department_ns.response(404, 'Company not found', error_model)
    def get(self):
        """
        Get all departments for a company

        This endpoint returns all departments for a specific company.
        Requires authentication.
        """
        pass

    @department_ns.doc('create_department', security='Bearer Auth')
    @department_ns.expect(create_department_input)
    @department_ns.response(200, 'Department created')
    @department_ns.response(400, 'Bad request', error_model)
    @department_ns.response(404, 'Company not found', error_model)
    @department_ns.response(500, 'Server error', error_model)
    def post(self):
        """
        Create a new department

        This endpoint creates a new department for a specific company.
        Requires admin or HR role.
        """
        pass

@department_ns.route('/api/departments/<string:department_id>')
@department_ns.param('department_id', 'The department identifier')
@department_ns.param('company_id', 'The company identifier')
class DepartmentResource(Resource):
    @department_ns.doc('get_department', security='Bearer Auth')
    @department_ns.response(200, 'Success')
    @department_ns.response(400, 'Bad request', error_model)
    @department_ns.response(404, 'Department or company not found', error_model)
    def get(self, department_id):
        """
        Get a department by ID

        This endpoint returns a department by ID for a specific company.
        Requires authentication.
        """
        pass

    @department_ns.doc('update_department', security='Bearer Auth')
    @department_ns.expect(create_department_input)
    @department_ns.response(200, 'Department updated')
    @department_ns.response(400, 'Bad request', error_model)
    @department_ns.response(404, 'Department or company not found', error_model)
    @department_ns.response(500, 'Server error', error_model)
    def put(self, department_id):
        """
        Update a department

        This endpoint updates a department for a specific company.
        Requires admin or HR role.
        """
        pass

    @department_ns.doc('delete_department', security='Bearer Auth')
    @department_ns.response(200, 'Department deleted')
    @department_ns.response(400, 'Bad request', error_model)
    @department_ns.response(404, 'Department or company not found', error_model)
    @department_ns.response(500, 'Server error', error_model)
    def delete(self, department_id):
        """
        Delete a department

        This endpoint deletes a department for a specific company.
        Requires admin or HR role.
        """
        pass

# ==================== DEVICE API ENDPOINTS ====================
@device_ns.route('/initSystem')
@device_ns.param('deviceSn', 'The device serial number')
class InitSystem(Resource):
    @device_ns.doc('init_system', security='Bearer Auth')
    @device_ns.response(200, 'System initialized successfully')
    @device_ns.response(500, 'Server error', error_model)
    def get(self):
        """
        Initialize the system

        This endpoint initializes the system for a specific device.
        It will delete all users and all logs. However, the settings will stay intact.
        Requires authentication.
        """
        pass

# ==================== SDK GENERATOR ENDPOINTS ====================
@sdk_ns.route('/languages')
class SdkLanguages(Resource):
    @sdk_ns.doc('get_sdk_languages')
    @sdk_ns.response(200, 'Success', api.model('SdkLanguagesResponse', {
        'languages': fields.List(fields.Nested(api.model('Language', {
            'id': fields.String(description='Language identifier'),
            'name': fields.String(description='Language name')
        })))
    }))
    def get(self):
        """
        Get supported SDK languages

        This endpoint returns a list of programming languages for which SDKs can be generated.
        """
        pass

@sdk_ns.route('/generate/<string:language>')
@sdk_ns.param('language', 'The programming language for the SDK')
@sdk_ns.param('base_url', 'The base URL for the API (optional)')
class SdkGenerator(Resource):
    @sdk_ns.doc('generate_sdk')
    @sdk_ns.response(200, 'SDK generated successfully')
    @sdk_ns.response(400, 'Language not supported', error_model)
    @sdk_ns.response(500, 'Server error', error_model)
    def get(self, language):
        """
        Generate an SDK for a specific language

        This endpoint generates and returns a downloadable SDK for the specified programming language.
        The SDK includes client libraries for all API endpoints.
        """
        pass

@sdk_ns.route('/sample/<string:language>/<string:endpoint>')
@sdk_ns.param('language', 'The programming language for the sample code')
@sdk_ns.param('endpoint', 'The endpoint for which to generate sample code')
class SdkSample(Resource):
    @sdk_ns.doc('get_sdk_sample')
    @sdk_ns.response(200, 'Sample code generated successfully')
    @sdk_ns.response(400, 'Language or endpoint not supported', error_model)
    def get(self, language, endpoint):
        """
        Get sample code for an endpoint

        This endpoint returns sample code for a specific endpoint in the specified programming language.
        """
        from application.api_doc_decorators import documented_endpoints, generate_code_samples

        # Get the base URL from the request
        base_url = request.args.get('base_url', request.url_root.rstrip('/'))

        # Find the endpoint
        endpoint_info = None
        for key, info in documented_endpoints.items():
            if key == endpoint or info.get('url', '').endswith(endpoint):
                endpoint_info = info
                break

        if not endpoint_info:
            return {'error': f'Endpoint {endpoint} not found'}, 400

        # Generate the sample code
        try:
            code = generate_code_samples(language, endpoint_info, base_url)
            return {'language': language, 'endpoint': endpoint, 'code': code}
        except Exception as e:
            return {'error': str(e)}, 400

# Add a route to serve the API sample code generator page
@api_doc.route('/sample-generator')
def sample_generator():
    """Serve the API sample code generator page."""
    return render_template('api_sample.html')

# Function to register all documented endpoints
def register_documented_endpoints():
    """
    Register all documented endpoints with the API.
    This should be called after all routes are registered.
    """
    from application.api_doc_decorators import register_documented_endpoints as register_endpoints

    # Get the Flask app
    app = current_app._get_current_object()

    # Register the endpoints
    register_endpoints(app)

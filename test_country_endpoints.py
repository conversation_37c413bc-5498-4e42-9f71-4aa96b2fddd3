#!/usr/bin/env python3
"""
Test script for country-specific payroll endpoints.
This script tests the new country-specific API endpoints.
"""

import requests
import json
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:9000"
TEST_COUNTRY_CODE = "RW"  # Rwanda

def test_country_endpoints():
    """Test the new country-specific endpoints."""
    
    print("🧪 Testing Country-Specific Payroll Endpoints")
    print("=" * 50)
    
    # Test data
    headers = {
        "Content-Type": "application/json",
        # Note: You'll need to add Authorization header with valid JWT token
        # "Authorization": "Bearer YOUR_JWT_TOKEN_HERE"
    }
    
    # Test 1: Get country employee types
    print("\n1. Testing GET /api/countries/{}/employee-types".format(TEST_COUNTRY_CODE))
    try:
        response = requests.get(
            f"{BASE_URL}/api/countries/{TEST_COUNTRY_CODE}/employee-types",
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('employee_types', []))} employee types")
            print(f"   Country: {data.get('country', {}).get('name')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 2: Get country tax policies
    print("\n2. Testing GET /api/countries/{}/tax-policies".format(TEST_COUNTRY_CODE))
    try:
        response = requests.get(
            f"{BASE_URL}/api/countries/{TEST_COUNTRY_CODE}/tax-policies",
            headers=headers,
            params={"date": "2025-01-01"}
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('policies', []))} tax policies")
            print(f"   Country: {data.get('country', {}).get('name')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 3: Get country deduction policies
    print("\n3. Testing GET /api/countries/{}/deduction-policies".format(TEST_COUNTRY_CODE))
    try:
        response = requests.get(
            f"{BASE_URL}/api/countries/{TEST_COUNTRY_CODE}/deduction-policies",
            headers=headers,
            params={"date": "2025-01-01"}
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {len(data.get('deduction_policies', []))} deduction policies")
            print(f"   Country: {data.get('country', {}).get('name')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 4: Create employee type (requires authentication)
    print("\n4. Testing POST /api/countries/{}/employee-types".format(TEST_COUNTRY_CODE))
    employee_type_data = {
        "name": "Test Employee Type",
        "code": "TEST_EMP",
        "description": "Test employee type for API testing",
        "is_default": False,
        "sort_order": 10
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/countries/{TEST_COUNTRY_CODE}/employee-types",
            headers=headers,
            json=employee_type_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"   Created employee type: {data.get('employee_type', {}).get('name')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test 5: Create tax policy (requires authentication)
    print("\n5. Testing POST /api/countries/{}/tax-policies".format(TEST_COUNTRY_CODE))
    tax_policy_data = {
        "policy_type_code": "PAYE",
        "employee_type_id": "test-employee-type-uuid",  # You'll need a real UUID
        "effective_from": "2025-01-01",
        "change_reason": "Test tax policy creation",
        "tax_brackets": [
            {
                "bracket_order": 1,
                "min_amount": 0,
                "max_amount": 360000,
                "tax_rate": 0.0
            },
            {
                "bracket_order": 2,
                "min_amount": 360001,
                "max_amount": 1200000,
                "tax_rate": 0.2
            }
        ]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/countries/{TEST_COUNTRY_CODE}/tax-policies",
            headers=headers,
            json=tax_policy_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"   Created tax policy with {len(data.get('policy', {}).get('tax_brackets', []))} brackets")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Exception: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Country endpoint testing completed!")
    print("\nNote: Some tests may fail due to authentication requirements.")
    print("To fully test, add a valid JWT token to the Authorization header.")

def print_endpoint_summary():
    """Print a summary of all new endpoints."""
    
    print("\n🚀 New Country-Specific Endpoints Summary")
    print("=" * 50)
    
    endpoints = [
        {
            "method": "GET",
            "path": "/api/countries/{country_code}/employee-types",
            "description": "Get all employee types for a country",
            "auth": "Required (admin, country_admin, hr)"
        },
        {
            "method": "POST", 
            "path": "/api/countries/{country_code}/employee-types",
            "description": "Create new employee type for a country",
            "auth": "Required (admin, country_admin)"
        },
        {
            "method": "GET",
            "path": "/api/countries/{country_code}/tax-policies",
            "description": "Get all tax policies for a country",
            "auth": "Required (admin, country_admin)"
        },
        {
            "method": "POST",
            "path": "/api/countries/{country_code}/tax-policies", 
            "description": "Create new tax policy for a country",
            "auth": "Required (admin, country_admin)"
        },
        {
            "method": "GET",
            "path": "/api/countries/{country_code}/deduction-policies",
            "description": "Get all deduction policies for a country",
            "auth": "Required (admin, country_admin, hr)"
        },
        {
            "method": "POST",
            "path": "/api/countries/{country_code}/deduction-policies",
            "description": "Create new deduction policy for a country", 
            "auth": "Required (admin, country_admin)"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n{endpoint['method']} {endpoint['path']}")
        print(f"   📝 {endpoint['description']}")
        print(f"   🔒 {endpoint['auth']}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print_endpoint_summary()
    
    # Uncomment to run tests (requires server to be running)
    # test_country_endpoints()

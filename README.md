# KaziSync HRMS - Comprehensive Human Resource Management System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.1.0-green.svg)](https://flask.palletsprojects.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org)
[![WebSocket](https://img.shields.io/badge/WebSocket-Real--time-orange.svg)]()
[![Azure](https://img.shields.io/badge/Azure-Cloud--Ready-blue.svg)]()
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

> **A comprehensive, multi-tenant HRMS solution built specifically for African businesses with local compliance, biometric integration, and enterprise-grade features. Single-handedly developed with complete DevOps infrastructure in under 3 months.**

## 🌟 System Overview

KaziSync HRMS is a sophisticated Human Resource Management System designed to handle the complete employee lifecycle from recruitment to retirement. Built with a multi-tenant architecture, it serves multiple companies while maintaining complete data isolation and customization capabilities.

### Key Features

- **🏢 Multi-Tenant Architecture** - Isolated databases per company with automatic provisioning
- **💰 Advanced Payroll Engine** - Multi-country compliance (Rwanda, Uganda) with decimal precision
- **⏰ Biometric Integration** - Real-time attendance tracking via WebSocket with 500+ device support
- **📊 Comprehensive Analytics** - Advanced reporting and dashboards across all modules
- **🔒 Enterprise Security** - JWT authentication, role-based access control, and complete audit trails
- **🌍 Multi-Country Support** - Localized compliance and regulations with automatic policy setup
- **💼 Complete HR Suite** - 9 integrated modules covering entire employee lifecycle
- **🚀 Production Ready** - Azure deployment with load balancers and database replication
- **📱 API-First Design** - RESTful APIs with comprehensive documentation and Postman collections
- **🔄 Real-time Sync** - WebSocket-based real-time communication for biometric devices
- **📋 Approval Workflows** - Flexible multi-level approval systems across all modules
- **💾 Document Management** - Azure Blob Storage integration for secure document handling

## 🏗️ System Architecture

### Multi-Tenant Design

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Central DB    │    │  Company A DB   │    │  Company B DB   │
│                 │    │                 │    │                 │
│ • Companies     │    │ • Employees     │    │ • Employees     │
│ • Users         │    │ • Payroll       │    │ • Payroll       │
│ • Countries     │    │ • Attendance    │    │ • Attendance    │
│ • Global Config │    │ • Leave Mgmt    │    │ • Leave Mgmt    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

- **Backend**: Flask 3.1.0 with SQLAlchemy 2.0.37 ORM
- **Database**: PostgreSQL with dual Alembic migration systems (central + tenant)
- **Real-time**: WebSocket (flask-sock) support for biometric devices
- **Authentication**: JWT (PyJWT 2.10.1) tokens with role-based access control
- **API**: RESTful APIs organized in blueprints with flask-restx documentation
- **Deployment**: Azure Cloud with Gunicorn, load balancers, and auto-scaling
- **Storage**: Azure Blob Storage for multi-tenant document management
- **Caching**: Redis integration for performance optimization
- **Monitoring**: Azure Application Insights with comprehensive logging
- **Security**: CORS enabled, security headers, and encrypted communications

## 📋 Core Modules

### 1. Employee Management
Complete employee lifecycle management with biometric device synchronization and multi-tenant support.

**Features:**
- Employee profiles with department assignment and hierarchy
- Real-time biometric device integration for attendance tracking
- Multi-country employee type support (Rwanda, Uganda)
- Comprehensive document management with Azure Blob Storage
- Employee self-service portal with role-based permissions
- Company user management with JWT authentication
- Employee shift management and scheduling
- Department-based organizational structure

**Key Models:**
- `Employee` - Core employee information with multi-tenant isolation
- `Department` - Organizational structure and hierarchy
- `Person` - Biometric device integration and synchronization
- `EnrollInfo` - Biometric enrollment data and device management
- `CompanyUser` - User authentication and role management

**API Endpoints:**
- `/api/employees` - CRUD operations with pagination and filtering
- `/api/company-users` - User management and authentication
- `/api/departments` - Department management and hierarchy

### 2. Payroll Management
Advanced payroll calculation engine with multi-country compliance and decimal precision.

**Features:**
- Dynamic payroll calculations (forward/backward from net, gross, or total cost)
- Multi-country tax compliance (Rwanda, Uganda) with automatic policy setup
- Historical rate versioning with effective dates for accurate calculations
- Decimal precision for all financial calculations (no rounding errors)
- Statutory deductions (PAYE, pension, CBHI, RAMA) with country-specific rates
- Comprehensive payroll analytics with department and trend analysis
- Payroll processing with batch runs and approval workflows
- Integration with loan management for automatic deductions
- Payslip generation with detailed breakdowns

**Key Models:**
- `EmployeeSalary` - Salary structures with allowances and deductions
- `PayrollRun` - Payroll processing batches with approval workflows
- `Payslip` - Individual pay statements with detailed calculations
- `PayrollPolicy` - Country-specific tax and deduction policies
- `PayrollDeduction` - Unified deduction management system
- `CalculationRule` - Flexible calculation rules engine

**API Endpoints:**
- `/api/payroll/calculate` - Dynamic payroll calculations
- `/api/payroll/runs` - Payroll batch processing
- `/api/payroll/analytics` - Comprehensive payroll analytics
- `/api/payroll/policies` - Tax and deduction policy management

### 3. Time & Attendance
Real-time attendance tracking with biometric device integration and WebSocket communication.

**Features:**
- Real-time biometric device communication via WebSocket (supports 500+ devices)
- Live attendance monitoring with instant updates
- Comprehensive shift management and scheduling system
- Automatic overtime calculations with configurable rules
- Advanced attendance analytics and reporting dashboards
- Device management with remote control capabilities
- Employee enrollment and biometric data synchronization
- Access control integration with door management

**Key Models:**
- `Attendance` - Daily attendance records with real-time sync
- `Shift` - Work schedule definitions with flexible timing
- `EmployeeShift` - Employee shift assignments and rotations
- `Device` - Biometric device management and configuration
- `Person` - Employee biometric data and device enrollment
- `EnrollInfo` - Biometric enrollment information and templates

**API Endpoints:**
- `/api/attendance` - Attendance record management
- `/api/shifts` - Shift scheduling and management
- `/api/employee-shifts` - Employee shift assignments
- **WebSocket**: `/pub/chat` - Real-time device communication

**Supported Device Commands:**
- User management (add, update, delete employees on devices)
- Attendance log retrieval (real-time and batch)
- Device configuration and remote control
- Door access control and management

### 4. Leave Management
Comprehensive leave policy management with approval workflows and country-specific compliance.

**Features:**
- Flexible leave policies by country/department with automatic setup
- Real-time leave balance tracking and accruals with decimal precision
- Multi-level approval workflows with configurable approval chains
- Leave calendar and planning with conflict detection
- Integration with payroll for automatic deductions
- Country-specific leave types (Rwanda Labor Law Article 59 compliance)
- Gender-specific leave validation (maternity/paternity)
- Leave balance doctor for maintenance and edge case handling
- Comprehensive leave analytics and reporting
- Leave audit trails for compliance and tracking

**Key Models:**
- `LeaveType` - Leave category definitions with country-specific rules
- `LeavePolicy` - Policy rules, accruals, and compliance settings
- `LeaveRequest` - Leave applications with approval workflows
- `LeaveBalance` - Employee leave balances with automatic calculations
- `LeaveAuditLog` - Complete audit trail for all leave activities

**API Endpoints:**
- `/api/leave/types` - Leave type management with HR permissions
- `/api/leave/policies` - Leave policy configuration
- `/api/leave/requests` - Leave application and approval workflows
- `/api/leave/balances` - Balance management and adjustments
- `/api/leave/analytics` - Comprehensive leave analytics
- `/api/leave/audit` - Audit trail and compliance reporting
- `/api/leave/balances/doctor` - Maintenance and balance correction

**Special Features:**
- Automatic Rwanda leave policy setup for new companies
- Leave balance doctor for fixing inconsistencies
- HR direct approval bypass for emergency situations
- Employee role-based approval permissions

### 5. Loan Management
Employee loan and advance management with automatic payroll deductions and flexible terms.

**Features:**
- Multiple loan types with configurable parameters (salary multipliers, interest rates)
- Flexible repayment schedules with employee-chosen installment periods
- Automatic payroll deductions with seamless integration
- Comprehensive loan analytics and reporting dashboards
- HR-configurable loan parameters (maximum amounts, terms, rates)
- Loan application workflows with approval processes
- Loan portfolio management and tracking
- Default management and collection workflows
- Decimal precision for all financial calculations

**Key Models:**
- `LoanType` - Loan category definitions with configurable parameters
- `EmployeeLoan` - Active loans with repayment tracking
- `LoanRepaymentSchedule` - Flexible payment schedules
- `LoanTransaction` - Complete payment history and tracking
- `LoanConfiguration` - HR-configurable loan parameters

**API Endpoints:**
- `/api/loans/management` - Loan application and management
- `/api/loans/configuration` - HR loan parameter configuration
- `/api/loans/analytics` - Comprehensive loan analytics
- `/api/loans/dashboard` - Loan portfolio overview

**Special Features:**
- Employee-customizable repayment terms
- Automatic payroll integration for deductions
- Loan balance tracking with interest calculations
- Portfolio analytics for HR decision-making

### 6. Recruitment Management
End-to-end recruitment process from job posting to hiring with integrated workflows.

**Features:**
- Job requisition and approval workflows with multi-level approvals
- Multi-channel job posting with LinkedIn integration
- Comprehensive candidate relationship management (CRM)
- Interview scheduling with Calendly, Zoom, and Google Meet integration
- Skills assessment platform with customizable tests
- Document management for candidate portfolios
- Recruitment analytics and hiring funnel tracking
- Application tracking system (ATS) with status management
- Collaborative hiring with team feedback and scoring

**Key Models:**
- `JobRequisition` - Job opening requests with approval workflows
- `JobPosting` - Published job opportunities with multi-channel distribution
- `Candidate` - Comprehensive candidate profiles and history
- `JobApplication` - Application tracking with status management
- `Interview` - Interview scheduling and management
- `Assessment` - Skills assessment and evaluation
- `RecruitmentDocument` - Document management for candidates

**API Endpoints:**
- `/api/recruitment/job-requisitions` - Job opening management
- `/api/recruitment/job-postings` - Job posting and distribution
- `/api/recruitment/candidates` - Candidate management
- `/api/recruitment/applications` - Application tracking
- `/api/recruitment/interviews` - Interview scheduling
- `/api/recruitment/assessments` - Skills assessment management
- `/api/recruitment/documents` - Document management
- `/api/recruitment/analytics` - Recruitment analytics and reporting

### 7. Onboarding Management
Structured onboarding workflows with document management and compliance tracking.

**Features:**
- Configurable onboarding workflows with company-specific requirements
- Document collection and verification with Azure Blob Storage integration
- Task assignment and tracking with automated notifications
- Multi-tenant document organization with separate folders per company
- Compliance tracking and audit trails
- International/universal onboarding processes
- Document requirement configuration by HR
- Progress tracking and completion monitoring
- Integration with employee management for seamless transitions

**Key Models:**
- `OnboardingWorkflow` - Configurable process definitions
- `OnboardingInstance` - Active onboarding processes with tracking
- `OnboardingTask` - Individual tasks with status management
- `OnboardingDocument` - Required documents with verification status

**API Endpoints:**
- `/api/onboarding/workflows` - Workflow configuration and management
- `/api/onboarding/instances` - Active onboarding process management
- `/api/onboarding/analytics` - Onboarding completion and analytics

**Special Features:**
- Company-configurable document requirements
- Azure Blob Storage integration for secure document storage
- Multi-tenant document organization
- Automated workflow progression and notifications

### 8. Performance Management
Employee performance tracking and development planning.

**Features:**
- Performance review cycles
- Goal setting and tracking
- 360-degree feedback
- Development planning
- Performance analytics

**Key Models:**
- `PerformanceCycle` - Review periods
- `PerformanceReview` - Individual reviews
- `PerformanceGoal` - Objective setting
- `PerformanceFeedback` - Feedback collection

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- PostgreSQL 13+
- Redis (for caching)
- Azure account (for blob storage)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd attend
```

2. **Create virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Environment Configuration**
Create a `.env` file in the root directory:
```env
# Database Configuration
SQLALCHEMY_DATABASE_URI=postgresql://user:password@localhost/central_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key

# Azure Storage
AZURE_STORAGE_CONNECTION_STRING=your-azure-connection-string
AZURE_CONTAINER_NAME=hrms-documents

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
```

5. **Database Setup**
```bash
# Initialize central database
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

# Initialize company database template
python -m alembic -c alembic_company.ini upgrade head
```

6. **Run the application**
```bash
# Development
python app.py

# Production with Gunicorn
gunicorn -c gunicorn.conf.py app:app
```

## 📁 Project Structure

```
attend/
├── app.py                          # Main application entry point
├── requirements.txt                # Python dependencies
├── alembic.ini                    # Central DB migrations config
├── alembic_company.ini            # Company DB migrations config
├── gunicorn.conf.py               # Production server config
│
├── application/                   # Main application package
│   ├── __init__.py
│   ├── database.py               # Database configuration
│   ├── config/                   # Configuration management
│   ├── Models/                   # Database models
│   │   ├── company.py           # Company and device models
│   │   ├── user.py              # User authentication
│   │   ├── employees/           # Employee-related models
│   │   ├── recruitment/         # Recruitment models
│   │   ├── onboarding/          # Onboarding models
│   │   └── performance/         # Performance models
│   │
│   ├── Routes/                   # API endpoints (blueprints)
│   │   ├── employees/           # Employee management APIs
│   │   ├── payroll/             # Payroll processing APIs
│   │   ├── leave/               # Leave management APIs
│   │   ├── loans/               # Loan management APIs
│   │   ├── recruitment/         # Recruitment APIs
│   │   ├── onboarding/          # Onboarding APIs
│   │   └── performance/         # Performance APIs
│   │
│   ├── Services/                 # Business logic services
│   │   ├── payroll_calculation_service.py
│   │   ├── PersonService.py     # Biometric integration
│   │   └── EnrollInfoService.py
│   │
│   ├── utils/                    # Utility functions
│   │   ├── db_connection.py     # Multi-tenant DB connection
│   │   └── username_encoder.py
│   │
│   ├── decorators/               # Custom decorators
│   │   ├── token_required.py    # JWT authentication
│   │   └── role_required.py     # Role-based access
│   │
│   └── Helpers/                  # Helper functions
│       ├── helper_methods.py
│       └── date_helper.py
│
├── alembic/                      # Central DB migrations
├── company_migrations/           # Company DB migrations
├── static/                       # Static files
├── templates/                    # HTML templates
└── docs/                         # Documentation
    ├── payroll_management_guide.md
    ├── leave_management_guide.md
    ├── loan_management_guide.md
    ├── recruitment_management_guide.md
    ├── onboarding_management_guide.md
    └── performance_management_guide.md
```

## 🔧 Configuration

### Database Configuration

The system uses a multi-tenant architecture with separate databases:

1. **Central Database**: Stores companies, users, and global configurations
2. **Company Databases**: Individual databases for each company's operational data

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SQLALCHEMY_DATABASE_URI` | Central database connection string | Yes |
| `DB_USER` | Database username | Yes |
| `DB_PASSWORD` | Database password | Yes |
| `DB_HOST` | Database host | Yes |
| `SECRET_KEY` | Flask secret key | Yes |
| `JWT_SECRET_KEY` | JWT token secret | Yes |
| `AZURE_STORAGE_CONNECTION_STRING` | Azure blob storage connection | Yes |
| `MAIL_SERVER` | SMTP server for emails | No |
| `MAIL_USERNAME` | Email username | No |
| `MAIL_PASSWORD` | Email password | No |

### Multi-Tenant Database Setup

Each company gets its own database automatically created when:
1. A new company is registered in the system
2. The database name follows the pattern: `company_{company_id}`
3. All company-specific tables are created using Alembic migrations

## 📡 API Documentation

### Authentication

All API endpoints require JWT authentication except for login and registration.

```bash
# Login to get JWT token
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password"
}

# Use token in subsequent requests
Authorization: Bearer <jwt_token>
```

### Core API Endpoints

#### Employee Management
```bash
# Get employees with pagination
GET /api/employees?company_id={uuid}&page=1&per_page=10

# Create new employee
POST /api/employees
{
  "company_id": "uuid",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "department_id": "uuid"
}

# Update employee
PATCH /api/employees/{employee_id}

# Delete employee
DELETE /api/employees/{employee_id}
```

#### Payroll Management
```bash
# Calculate payroll for employee
POST /api/payroll/calculate
{
  "employee_id": "uuid",
  "calculation_date": "2024-01-31",
  "calculation_type": "forward"
}

# Create payroll run
POST /api/payroll/runs
{
  "company_id": "uuid",
  "pay_period_start": "2024-01-01",
  "pay_period_end": "2024-01-31"
}

# Get payroll analytics
GET /api/payroll/analytics/overview?company_id={uuid}&period=month
```

#### Leave Management
```bash
# Submit leave request
POST /api/leave/requests
{
  "employee_id": "uuid",
  "leave_type_id": "uuid",
  "start_date": "2024-02-01",
  "end_date": "2024-02-05"
}

# Approve/reject leave
PUT /api/leave/requests/{request_id}/approve
```

#### Loan Management
```bash
# Apply for loan
POST /api/loans/applications
{
  "employee_id": "uuid",
  "loan_type_id": "uuid",
  "amount": 500000,
  "repayment_months": 12
}

# Get loan analytics
GET /api/loans/analytics/overview?company_id={uuid}
```

## 🔌 Biometric Device Integration

### WebSocket Communication

The system communicates with biometric devices via WebSocket for real-time attendance tracking.

#### Device Registration
```javascript
// Device connects and registers
{
  "cmd": "reg",
  "sn": "device_serial_number"
}

// Server response
{
  "ret": "reg",
  "result": true,
  "cloudtime": "2024-01-15 10:30:00"
}
```

#### Attendance Data
```javascript
// Device sends attendance records
{
  "cmd": "sendlog",
  "sn": "device_serial_number",
  "count": 1,
  "record": [{
    "enrollid": 123,
    "time": "2024-01-15 08:30:00",
    "mode": 1,
    "inout": 1,
    "event": 1
  }]
}
```

#### User Management
```javascript
// Send user to device
{
  "cmd": "setuserinfo",
  "enrollid": 123,
  "name": "John Doe",
  "backupnum": 50,
  "admin": 0,
  "record": "base64_encoded_biometric_data"
}
```

### Supported Device Commands

| Command | Description |
|---------|-------------|
| `getuserlist` | Get all users from device |
| `getuserinfo` | Get specific user info |
| `setuserinfo` | Send user to device |
| `deleteuser` | Remove user from device |
| `getalllog` | Get all attendance records |
| `getnewlog` | Get new attendance records |
| `opendoor` | Remote door control |

## 🌍 Multi-Country Support

### Rwanda Configuration
```python
# Tax brackets (2024)
RWANDA_PAYE_BRACKETS = [
    {"min": 0, "max": 30000, "rate": 0.00},
    {"min": 30001, "max": 100000, "rate": 0.20},
    {"min": 100001, "max": *********, "rate": 0.30}
]

# Statutory deductions
RWANDA_DEDUCTIONS = {
    "pension_employee": 0.03,  # 3%
    "pension_employer": 0.05,  # 5%
    "cbhi_employee": 0.03,     # 3%
    "cbhi_employer": 0.03,     # 3%
    "rama_employee": 0.01,     # 1%
    "rama_employer": 0.01      # 1%
}
```

### Uganda Configuration
```python
# Tax brackets (2024)
UGANDA_PAYE_BRACKETS = [
    {"min": 0, "max": 235000, "rate": 0.00},
    {"min": 235001, "max": 335000, "rate": 0.10},
    {"min": 335001, "max": 410000, "rate": 0.20},
    {"min": 410001, "max": *********, "rate": 0.30}
]

# Statutory deductions
UGANDA_DEDUCTIONS = {
    "nssf_employee": 0.05,     # 5%
    "nssf_employer": 0.10,     # 10%
    "local_service_tax": 25000  # Fixed amount
}
```

## 🚀 Deployment

### Azure Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│  App Service 1  │    │  App Service 2  │
│                 │    │                 │    │                 │
│ • SSL Termination│    │ • Flask App     │    │ • Flask App     │
│ • Health Checks │    │ • WebSocket     │    │ • WebSocket     │
│ • Auto Scaling  │    │ • API Endpoints │    │ • API Endpoints │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  PostgreSQL     │
                    │                 │
                    │ • Primary DB    │
                    │ • Read Replicas │
                    │ • Automated     │
                    │   Backups       │
                    └─────────────────┘
```

### Production Configuration

```bash
# Gunicorn configuration
bind = "0.0.0.0:8000"
workers = 4
worker_class = "gevent"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
```

### Environment Setup

```bash
# Production environment variables
export FLASK_ENV=production
export FLASK_DEBUG=0
export SQLALCHEMY_DATABASE_URI="******************************"
export REDIS_URL="redis://redis-host:6379/0"
export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;..."
```

## 📊 Performance & Monitoring

### Key Metrics

- **Response Time**: < 200ms for API endpoints
- **Uptime**: 99.9% availability target
- **Concurrent Users**: Supports 1000+ concurrent users
- **Database Performance**: < 50ms query response time
- **WebSocket Connections**: Supports 500+ concurrent device connections

### Monitoring Stack

- **Application Monitoring**: Azure Application Insights
- **Database Monitoring**: PostgreSQL built-in monitoring
- **Infrastructure Monitoring**: Azure Monitor
- **Log Management**: Centralized logging with Azure Log Analytics
- **Error Tracking**: Integrated error reporting and alerting

## 🔒 Security Features

### Authentication & Authorization

- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access Control**: Granular permissions system
- **Multi-Factor Authentication**: Optional 2FA support
- **Session Management**: Secure session handling

### Data Protection

- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: TLS 1.3 for all communications
- **Data Isolation**: Complete tenant data separation
- **Backup Encryption**: Encrypted automated backups
- **GDPR Compliance**: Data protection and privacy controls

### Security Headers

```python
# Security headers implemented
{
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'"
}
```

## 📈 Analytics & Reporting

### Built-in Analytics

Each module includes comprehensive analytics:

- **Payroll Analytics**: Cost analysis, department breakdowns, trend analysis
- **Leave Analytics**: Usage patterns, balance tracking, approval metrics
- **Loan Analytics**: Repayment tracking, default analysis, portfolio overview
- **Recruitment Analytics**: Hiring funnel, source effectiveness, time-to-hire
- **Performance Analytics**: Review completion, goal achievement, development tracking

### Custom Reports

- **Executive Dashboard**: High-level KPIs and metrics
- **Department Reports**: Department-specific analytics
- **Compliance Reports**: Regulatory compliance tracking
- **Financial Reports**: Payroll costs, loan portfolios, budget analysis
- **Operational Reports**: Attendance patterns, leave utilization, productivity metrics

## 🧪 Testing

### Test Structure

```bash
# Run all tests
python -m pytest

# Run specific test modules
python -m pytest tests/test_payroll.py
python -m pytest tests/test_employees.py
python -m pytest tests/test_leave.py

# Run with coverage
python -m pytest --cov=application tests/
```

### Test Categories

- **Unit Tests**: Individual function and method testing
- **Integration Tests**: API endpoint testing
- **Database Tests**: Model and query testing
- **WebSocket Tests**: Real-time communication testing
- **Security Tests**: Authentication and authorization testing

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/new-feature`
3. **Make changes and add tests**
4. **Run test suite**: `python -m pytest`
5. **Submit pull request**

### Code Standards

- **PEP 8**: Python code style guidelines
- **Type Hints**: Use type annotations where applicable
- **Documentation**: Comprehensive docstrings for all functions
- **Testing**: Minimum 80% code coverage required
- **Security**: Security review for all changes

## 📚 Documentation

### Available Guides

- [Payroll Management Guide](./payroll_management_guide.md) - Complete payroll system documentation
- [Leave Management Guide](./leave_management_guide.md) - Leave policies and workflows
- [Loan Management Guide](./loanmanagement_guide.md) - Employee loan system
- [Recruitment Management Guide](./recruitment_management_guide.md) - Hiring process management
- [Onboarding Management Guide](./onboarding_management_guide.md) - Employee onboarding workflows
- [Performance Management Guide](./performance_management_guide.md) - Performance review system

### API Documentation

- **Interactive API Docs**: Available at `/api/docs` when running the application
- **Postman Collection**: Import collection for API testing
- **OpenAPI Specification**: Full API specification available

## 🆘 Support & Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check database connectivity
python -c "from application.database import central_db; print('DB Connected')"

# Verify environment variables
python -c "import os; print(os.getenv('SQLALCHEMY_DATABASE_URI'))"
```

#### WebSocket Connection Issues
```bash
# Check WebSocket endpoint
wscat -c ws://localhost:5000/pub/chat

# Verify device registration
curl -X GET "http://localhost:5000/device"
```

#### Migration Issues
```bash
# Reset migrations (development only)
flask db downgrade
flask db upgrade

# Company database migrations
python -m alembic -c alembic_company.ini upgrade head
```

### Getting Help

- **Documentation**: Check the comprehensive guides in `/docs`
- **Issues**: Report bugs and feature requests via GitHub issues
- **Support**: Contact support team for enterprise customers
- **Community**: Join our developer community for discussions

## 📄 License

This project is proprietary software. All rights reserved.

## 🏆 Acknowledgments

- **Flask Community**: For the excellent web framework
- **SQLAlchemy Team**: For the powerful ORM
- **PostgreSQL**: For the robust database system
- **Azure**: For reliable cloud infrastructure
- **Open Source Community**: For the various libraries and tools used

---

**Built with ❤️ for African businesses by the KaziSync team**

> *Transforming HR management across Africa, one company at a time.*

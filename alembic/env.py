from logging.config import fileConfig
import os
from dotenv import load_dotenv

from sqlalchemy import create_engine
from sqlalchemy import pool
from application.database import central_db

from alembic import context

# Load environment variables
load_dotenv()

# Import central database models so Alembic can detect them for autogeneration
# These imports are required for Alembic to discover the models, even if not directly used

# Core central models
from application.Models.company import Company, CompanyDevice  # noqa: F401
from application.Models.country import Country  # noqa: F401
from application.Models.user import User  # noqa: F401
from application.Models.refreshtoken import RefreshToken  # noqa: F401
from application.Models.associations import user_company_association  # noqa: F401

# Payroll-related central models
from application.Models.employee_type import EmployeeType  # noqa: F401
from application.Models.payroll_policy_type import PayrollPolicyType  # noqa: F401
from application.Models.payroll_policy import PayrollPolicy  # noqa: F401
from application.Models.tax_bracket import TaxBracket  # noqa: F401
from application.Models.deduction_type import DeductionType  # noqa: F401
from application.Models.deduction_policy import DeductionPolicy  # noqa: F401
from application.Models.calculation_rule import CalculationRule  # noqa: F401

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = central_db.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # Get the database URL from environment variables (same as Flask app)
    url = os.getenv("SQLALCHEMY_DATABASE_URI")

    if not url:
        raise ValueError("SQLALCHEMY_DATABASE_URI environment variable is not set")

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Get the database URL from environment variables (same as Flask app)
    database_url = os.getenv("SQLALCHEMY_DATABASE_URI")

    if not database_url:
        raise ValueError("SQLALCHEMY_DATABASE_URI environment variable is not set")

    connectable = create_engine(
        database_url,
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
